#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
导入默认设置管理器

用于管理数据导入功能的默认设置，包括：
- 起始行默认值
- 导入模式默认选择
- 工作表自动匹配设置
- 其他导入选项的默认值

创建时间: 2025-01-20
"""

import os
import json
from typing import Dict, Any, List, Optional
from pathlib import Path

from src.utils.log_config import setup_logger


class ImportDefaultsManager:
    """导入默认设置管理器"""
    
    def __init__(self, config_dir: str = "state/data"):
        """
        初始化导入默认设置管理器
        
        Args:
            config_dir: 配置文件目录
        """
        self.logger = setup_logger(__name__)
        self.config_dir = Path(config_dir)
        self.config_file = self.config_dir / "import_defaults.json"
        
        # 确保配置目录存在
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        # 默认设置
        self.default_settings = {
            'start_row': 1,
            'import_mode': 'multi_sheet',  # 'single_sheet' 或 'multi_sheet'
            'auto_match_sheet': True,
            'include_header': True,
            'skip_empty_rows': True,
            'create_table_mode': 'sheet_name',  # 'sheet_name', 'custom_name', 'preview_only'
            'import_strategy': 'merge_to_single_table',  # 'merge_to_single_table', 'separate_tables'
            'table_template': 'salary_data'  # 'salary_data', 'salary_changes'
        }
        
        # 人员类别与工作表名称的映射规则
        self.category_sheet_mapping = {
            '全部在职人员': ['全部在职人员', '在职人员', '全部人员', '所有人员', '全员', '在职'],
            '离休人员': ['离休人员', '离休', '离休干部', '离休职工'],
            '退休人员': ['退休人员', '退休', '退休职工', '退休干部'],
            'A岗职工': ['A岗职工', 'A岗', 'A类人员', 'A岗人员'],
            '临时工': ['临时工', '临时人员', '临聘人员', '临时职工'],
            '实习生': ['实习生', '实习人员', '见习生', '实习'],
            '管理层': ['管理层', '管理人员', '领导', '干部'],
            '教师': ['教师', '教职工', '教员', '老师'],
            '科研人员': ['科研人员', '科研', '研究人员', '科技人员'],
            '医务人员': ['医务人员', '医生', '护士', '医护', '医疗'],
            '技术人员': ['技术人员', '技术', '工程师', '技师']
        }
    
    def load_settings(self) -> Dict[str, Any]:
        """
        加载导入默认设置
        
        Returns:
            导入设置字典
        """
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    user_settings = json.load(f)
                
                # 合并用户设置和默认设置
                settings = self.default_settings.copy()
                settings.update(user_settings)
                
                self.logger.info("成功加载用户自定义导入设置")
                return settings
            else:
                self.logger.info("未找到用户设置文件，使用默认设置")
                return self.default_settings.copy()
                
        except Exception as e:
            self.logger.warning(f"加载导入设置失败，使用默认设置: {e}")
            return self.default_settings.copy()
    
    def save_settings(self, settings: Dict[str, Any]) -> bool:
        """
        保存导入设置
        
        Args:
            settings: 要保存的设置字典
            
        Returns:
            是否保存成功
        """
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)
            
            self.logger.info("导入设置保存成功")
            return True
            
        except Exception as e:
            self.logger.error(f"保存导入设置失败: {e}")
            return False
    
    def get_category_sheet_mapping(self) -> Dict[str, List[str]]:
        """
        获取人员类别与工作表名称的映射规则
        
        Returns:
            映射规则字典
        """
        return self.category_sheet_mapping.copy()
    
    def update_category_mapping(self, category: str, keywords: List[str]) -> bool:
        """
        更新人员类别的工作表匹配关键词
        
        Args:
            category: 人员类别名称
            keywords: 匹配关键词列表
            
        Returns:
            是否更新成功
        """
        try:
            self.category_sheet_mapping[category] = keywords
            
            # 保存到配置文件
            mapping_file = self.config_dir / "category_sheet_mapping.json"
            with open(mapping_file, 'w', encoding='utf-8') as f:
                json.dump(self.category_sheet_mapping, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"更新类别映射成功: {category} -> {keywords}")
            return True
            
        except Exception as e:
            self.logger.error(f"更新类别映射失败: {e}")
            return False
    
    def load_category_mapping(self) -> bool:
        """
        从配置文件加载类别映射
        
        Returns:
            是否加载成功
        """
        try:
            mapping_file = self.config_dir / "category_sheet_mapping.json"
            if mapping_file.exists():
                with open(mapping_file, 'r', encoding='utf-8') as f:
                    saved_mapping = json.load(f)
                
                self.category_sheet_mapping.update(saved_mapping)
                self.logger.info("成功加载类别映射配置")
                return True
            else:
                self.logger.info("未找到类别映射配置文件，使用默认映射")
                return True
                
        except Exception as e:
            self.logger.warning(f"加载类别映射失败，使用默认映射: {e}")
            return False
    
    def find_matching_sheet(self, category: str, sheet_names: List[str]) -> Optional[str]:
        """
        根据人员类别查找匹配的工作表
        
        Args:
            category: 人员类别名称
            sheet_names: 可用的工作表名称列表
            
        Returns:
            匹配的工作表名称，如果没有找到则返回None
        """
        if not category or not sheet_names:
            return None
        
        # 获取该类别的匹配关键词
        keywords = self.category_sheet_mapping.get(category, [category])
        
        best_match = None
        best_score = 0
        
        for sheet_name in sheet_names:
            for keyword in keywords:
                # 精确匹配
                if keyword == sheet_name:
                    self.logger.info(f"找到精确匹配: {category} -> {sheet_name}")
                    return sheet_name
                
                # 包含匹配
                if keyword in sheet_name:
                    score = len(keyword) / len(sheet_name)
                    if score > best_score:
                        best_score = score
                        best_match = sheet_name
                
                # 反向包含匹配
                if sheet_name in keyword:
                    score = len(sheet_name) / len(keyword)
                    if score > best_score:
                        best_score = score
                        best_match = sheet_name
        
        if best_match and best_score > 0.3:  # 设置最低匹配阈值
            self.logger.info(f"找到模糊匹配: {category} -> {best_match} (得分: {best_score:.2f})")
            return best_match
        
        self.logger.info(f"未找到匹配的工作表: {category}")
        return None
    
    def get_smart_defaults_for_category(self, category: str) -> Dict[str, Any]:
        """
        根据人员类别获取智能默认设置
        
        Args:
            category: 人员类别名称
            
        Returns:
            针对该类别优化的默认设置
        """
        settings = self.load_settings()
        
        # 根据类别调整设置
        if category in ['离休人员', '退休人员']:
            # 离退休人员通常数据较少，建议单Sheet模式
            settings['import_mode'] = 'single_sheet'
            settings['import_strategy'] = 'merge_to_single_table'
        elif category in ['临时工', '实习生']:
            # 临时人员可能需要特殊处理
            settings['import_mode'] = 'single_sheet'
            settings['create_table_mode'] = 'custom_name'
        else:
            # 其他类别使用多Sheet模式
            settings['import_mode'] = 'multi_sheet'
            settings['import_strategy'] = 'merge_to_single_table'
        
        return settings
    
    def reset_to_defaults(self) -> bool:
        """
        重置为默认设置
        
        Returns:
            是否重置成功
        """
        try:
            if self.config_file.exists():
                self.config_file.unlink()
            
            self.logger.info("已重置为默认导入设置")
            return True
            
        except Exception as e:
            self.logger.error(f"重置设置失败: {e}")
            return False
