2025-06-26 11:06:29.230 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-26 11:06:29.230 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-26 11:06:29.230 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-26 11:06:29.230 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-26 11:06:29.230 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-26 11:06:29.230 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-26 11:07:46.581 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-26 11:07:46.581 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-26 11:07:46.582 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-26 11:07:46.582 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-26 11:07:46.583 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-26 11:07:46.584 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-26 11:07:48.052 | INFO     | __main__:setup_app_logging:206 | 月度工资异动处理系统 v2.0.0-refactored 启动
2025-06-26 11:07:48.053 | INFO     | __main__:main:270 | 初始化核心管理器...
2025-06-26 11:07:48.054 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 11:07:48.054 | INFO     | src.modules.system_config.config_manager:load_config:338 | 正在加载配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 11:07:48.055 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:252 | 配置文件验证通过
2025-06-26 11:07:48.056 | INFO     | src.modules.system_config.config_manager:load_config:350 | 配置文件加载成功
2025-06-26 11:07:48.057 | INFO     | src.modules.data_storage.database_manager:_initialize_database:101 | 开始初始化数据库...
2025-06-26 11:07:48.076 | INFO     | src.modules.data_storage.database_manager:_initialize_database:156 | 数据库初始化完成
2025-06-26 11:07:48.080 | INFO     | src.modules.data_storage.database_manager:__init__:97 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-06-26 11:07:48.085 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 11:07:48.090 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:102 | 动态表管理器初始化完成
2025-06-26 11:07:48.092 | INFO     | __main__:main:275 | 核心管理器初始化完成。
2025-06-26 11:07:48.122 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成 - 最大条目数: 50, 最大内存: 50MB
2025-06-26 11:07:48.135 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-06-26 11:07:48.136 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-06-26 11:07:48.931 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:1493 | 菜单栏创建完成
2025-06-26 11:07:48.932 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 11:07:48.932 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设置: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 11:07:48.933 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成功
2025-06-26 11:07:48.947 | INFO     | src.gui.prototype.prototype_main_window:__init__:1469 | 菜单栏管理器初始化完成
2025-06-26 11:07:48.948 | INFO     | src.gui.table_header_manager:__init__:68 | 表头管理器初始化完成
2025-06-26 11:07:48.950 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:2170 | 管理器设置完成，包含增强版表头管理器
2025-06-26 11:07:48.997 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_state:151 | 导航状态加载成功
2025-06-26 11:07:49.000 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-06-26 11:07:49.108 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:_load_data:556 | 数据加载成功: state/user/tree_expansion_data.json
2025-06-26 11:07:49.109 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:148 | 智能树形展开算法初始化完成
2025-06-26 11:07:49.127 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:916 | 开始从元数据动态加载工资数据...
2025-06-26 11:07:49.129 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:923 | 检测到首次启动，将延迟加载导航数据确保数据库就绪
2025-06-26 11:07:49.163 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:654 | 导航面板已重构：移除功能性导航，专注数据导航
2025-06-26 11:07:49.182 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:689 | 恢复导航状态: 90个展开项
2025-06-26 11:07:49.211 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表', '异动人员表', '异动人员表 > 2025年 > 4月', '异动人员表 > 2025年']
2025-06-26 11:07:49.215 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:495 | 增强导航面板初始化完成
2025-06-26 11:07:49.313 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1264 | 快捷键注册完成: 18/18 个
2025-06-26 11:07:49.314 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1475 | 拖拽排序管理器初始化完成
2025-06-26 11:07:49.315 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 11:07:49.316 | INFO     | src.modules.data_import.header_edit_manager:__init__:74 | 表头编辑管理器初始化完成
2025-06-26 11:07:49.320 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1851 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-06-26 11:07:49.321 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 11:07:49.322 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 11:07:49.332 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 10.87ms
2025-06-26 11:07:49.333 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 11:07:49.378 | INFO     | src.gui.widgets.pagination_widget:__init__:88 | 分页组件初始化完成
2025-06-26 11:07:49.467 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:379 | 控制面板按钮信号连接完成
2025-06-26 11:07:49.488 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 11:07:49.489 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 11:07:49.490 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:2143 | 快捷键设置完成
2025-06-26 11:07:49.491 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:2132 | 主窗口UI设置完成。
2025-06-26 11:07:49.491 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:2198 | 信号连接设置完成
2025-06-26 11:07:49.493 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 11:07:49.554 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:2627 | 已加载字段映射信息，共44个表的映射
2025-06-26 11:07:49.554 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 11:07:49.554 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 11:07:49.556 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 11:07:49.556 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 2.04ms
2025-06-26 11:07:49.557 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 11:07:49.558 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 11:07:49.558 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 11:07:49.559 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 11:07:49.560 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 11:07:49.560 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 1.06ms
2025-06-26 11:07:49.561 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 11:07:49.562 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 11:07:49.562 | INFO     | src.gui.prototype.prototype_main_window:__init__:2084 | 原型主窗口初始化完成
2025-06-26 11:07:50.391 | INFO     | __main__:main:297 | 应用程序启动成功
2025-06-26 11:07:50.396 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1266px)
2025-06-26 11:07:50.397 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:1241 | MainWorkspaceArea 响应式适配: sm
2025-06-26 11:07:50.400 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_load_salary_data:938 | 执行延迟的工资数据加载...
2025-06-26 11:07:50.401 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1036 | 开始强制刷新工资数据导航... (尝试 1/3)
2025-06-26 11:07:50.401 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1044 | 找到工资表节点: 📊 工资表
2025-06-26 11:07:50.444 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:702 | 找到 68 个匹配类型 'salary_data' 的表
2025-06-26 11:07:50.447 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2026年，包含 5 个月份
2025-06-26 11:07:50.476 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2025年，包含 3 个月份
2025-06-26 11:07:50.477 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2024年，包含 1 个月份
2025-06-26 11:07:50.477 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2023年，包含 1 个月份
2025-06-26 11:07:50.477 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2022年，包含 1 个月份
2025-06-26 11:07:50.477 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2021年，包含 1 个月份
2025-06-26 11:07:50.477 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2020年，包含 1 个月份
2025-06-26 11:07:50.477 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2019年，包含 1 个月份
2025-06-26 11:07:50.477 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2018年，包含 1 个月份
2025-06-26 11:07:50.477 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2017年，包含 1 个月份
2025-06-26 11:07:50.477 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2016年，包含 1 个月份
2025-06-26 11:07:50.477 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1149 | 工资数据导航已强制刷新: 11 个年份, 17 个月份
2025-06-26 11:07:50.477 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1152 | 默认路径: 工资表 > 2026年 > 1月 > 全部在职人员
2025-06-26 11:07:50.477 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1191 | force_refresh_salary_data 执行完成
2025-06-26 11:08:03.727 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表', '异动人员表', '异动人员表 > 2025年 > 4月', '异动人员表 > 2025年']
2025-06-26 11:08:03.727 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年
2025-06-26 11:08:03.727 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 11:08:03.727 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 11:08:03.727 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 11:08:03.727 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 0.00ms
2025-06-26 11:08:03.727 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 11:08:03.727 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 11:08:03.727 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 11:08:03.727 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 11:08:03.727 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 11:08:03.727 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 0.00ms
2025-06-26 11:08:03.727 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 11:08:03.727 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 11:08:03.727 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年
2025-06-26 11:08:05.826 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2026年', '工资表', '异动人员表', '异动人员表 > 2025年 > 4月']
2025-06-26 11:08:05.826 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 1月
2025-06-26 11:08:05.826 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 11:08:05.826 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 11:08:05.826 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 11:08:05.826 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 0.00ms
2025-06-26 11:08:05.826 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 11:08:05.826 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 11:08:05.826 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 11:08:05.826 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 11:08:05.826 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 11:08:05.826 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 0.00ms
2025-06-26 11:08:05.826 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 11:08:05.826 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 11:08:05.826 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 1月
2025-06-26 11:08:07.040 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2026年 > 1月', '工资表 > 2026年', '工资表', '异动人员表']
2025-06-26 11:08:07.040 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 1月 > A岗职工
2025-06-26 11:08:07.041 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 11:08:07.042 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2757 | 数据量大(62条)，启用分页模式
2025-06-26 11:08:07.042 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2785 | 使用分页模式加载 salary_data_2026_01_a_grade_employees，第1页，每页50条
2025-06-26 11:08:07.043 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2828 | 缓存未命中，从数据库加载: salary_data_2026_01_a_grade_employees 第1页
2025-06-26 11:08:07.044 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 1月 > A岗职工
2025-06-26 11:08:07.044 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2026_01_a_grade_employees 第1页数据，每页50条
2025-06-26 11:08:07.046 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_01_a_grade_employees 分页获取数据: 第1页, 每页50条
2025-06-26 11:08:07.054 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_01_a_grade_employees 获取第1页数据: 50 行，总计62行
2025-06-26 11:08:07.054 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 11:08:07.056 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2643 | 表 salary_data_2026_01_a_grade_employees 没有保存的字段映射信息
2025-06-26 11:08:07.056 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 62
2025-06-26 11:08:07.057 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2858 | 分页数据加载成功（数据库）: 50条数据，第1页，总计62条
2025-06-26 11:08:07.062 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_01_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-26 11:08:07.063 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 11:08:07.064 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2696 | 应用表 salary_data_2026_01_a_grade_employees 的字段偏好: 7个字段 ['id', 'employee_id', 'employee_name', 'id_card', 'department', 'position', 'basic_salary']
2025-06-26 11:08:07.066 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-26 11:08:07.068 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_01_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-26 11:08:07.075 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 1000 -> 50
2025-06-26 11:08:07.076 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 11:08:07.078 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 7 列
2025-06-26 11:08:07.114 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 39.73ms
2025-06-26 11:08:07.144 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-26 11:08:07.144 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 62
2025-06-26 11:08:09.699 | ERROR    | src.gui.prototype.prototype_main_window:_refresh_table_data:1129 | 刷新表格数据失败: 'MainWorkspaceArea' object has no attribute 'current_data_path'
2025-06-26 11:08:12.404 | INFO     | src.gui.prototype.prototype_main_window:_on_refresh_data:420 | 刷新数据功能被触发，将强制刷新导航面板。
2025-06-26 11:08:12.404 | WARNING  | src.gui.prototype.prototype_main_window:_on_refresh_data:435 | 导航面板不支持强制刷新。
2025-06-26 11:08:22.900 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-26 11:08:22.900 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 11:08:22.915 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 11:08:22.915 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_01_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-26 11:08:22.915 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_01_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-26 11:08:23.172 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 12
2025-06-26 11:08:23.172 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(12)自动调整最大可见行数为: 12
2025-06-26 11:08:23.172 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 12 行, 16 列
2025-06-26 11:08:23.244 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 12 行, 最大可见行数: 12, 耗时: 329.11ms
2025-06-26 11:08:23.257 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第2页, 显示12条记录，字段数: 16
2025-06-26 11:08:23.258 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 2
2025-06-26 11:08:34.892 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-26 11:08:34.893 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 11:08:34.893 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 11:08:34.894 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_01_a_grade_employees 分页获取数据: 第1页, 每页50条
2025-06-26 11:08:34.896 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_01_a_grade_employees 获取第1页数据: 50 行，总计62行
2025-06-26 11:08:34.898 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 12 -> 50
2025-06-26 11:08:34.898 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 11:08:34.900 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 11:08:34.909 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 10.78ms
2025-06-26 11:08:34.909 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第1页, 显示50条记录，字段数: 16
2025-06-26 11:08:34.909 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 1
2025-06-26 11:08:50.270 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2026年 > 1月', '工资表 > 2026年', '工资表', '异动人员表']
2025-06-26 11:08:50.270 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 1月 > 离休人员
2025-06-26 11:08:50.270 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 11:08:50.270 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2760 | 数据量小(2条)，使用普通模式
2025-06-26 11:08:50.270 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 1月 > 离休人员
2025-06-26 11:08:50.270 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2919 | 统一加载所有字段（修复字段不一致问题）
2025-06-26 11:08:50.286 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2026_01_retired_employees 获取数据...
2025-06-26 11:08:50.286 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2026_01_retired_employees 获取 2 行数据。
2025-06-26 11:08:50.286 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2926 | 成功从数据库加载 2 行数据，16 个字段
2025-06-26 11:08:50.286 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2643 | 表 salary_data_2026_01_retired_employees 没有保存的字段映射信息
2025-06-26 11:08:50.286 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2931 | 应用字段映射后的表头: ['id', 'employee_id', 'employee_name', 'id_card', 'department', 'position', 'basic_salary', 'performance_bonus', 'overtime_pay', 'allowance', 'deduction', 'total_salary', 'month', 'year', 'created_at', 'updated_at']
2025-06-26 11:08:50.286 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 11:08:50.286 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2702 | 表 salary_data_2026_01_retired_employees 无字段偏好设置，显示所有字段
2025-06-26 11:08:50.302 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 2 行
2025-06-26 11:08:50.302 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2583 | 最大可见行数过小可能影响性能，建议至少设置为10，当前值: 2
2025-06-26 11:08:50.673 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 2
2025-06-26 11:08:50.673 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(2)自动调整最大可见行数为: 2
2025-06-26 11:08:50.704 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 2 行, 16 列
2025-06-26 11:08:50.704 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 2 行, 最大可见行数: 2, 耗时: 402.56ms
2025-06-26 11:08:50.719 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 2
2025-06-26 11:08:57.034 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2026年 > 1月', '工资表 > 2026年', '工资表', '异动人员表']
2025-06-26 11:08:57.034 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 1月 > 退休人员
2025-06-26 11:08:57.034 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 11:08:57.034 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2760 | 数据量小(13条)，使用普通模式
2025-06-26 11:08:57.034 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 1月 > 退休人员
2025-06-26 11:08:57.034 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2919 | 统一加载所有字段（修复字段不一致问题）
2025-06-26 11:08:57.034 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2026_01_pension_employees 获取数据...
2025-06-26 11:08:57.034 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2026_01_pension_employees 获取 13 行数据。
2025-06-26 11:08:57.034 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2926 | 成功从数据库加载 13 行数据，16 个字段
2025-06-26 11:08:57.034 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2643 | 表 salary_data_2026_01_pension_employees 没有保存的字段映射信息
2025-06-26 11:08:57.049 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2931 | 应用字段映射后的表头: ['id', 'employee_id', 'employee_name', 'id_card', 'department', 'position', 'basic_salary', 'performance_bonus', 'overtime_pay', 'allowance', 'deduction', 'total_salary', 'month', 'year', 'created_at', 'updated_at']
2025-06-26 11:08:57.049 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 11:08:57.049 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2702 | 表 salary_data_2026_01_pension_employees 无字段偏好设置，显示所有字段
2025-06-26 11:08:57.049 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 13 行
2025-06-26 11:08:57.049 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 2 -> 13
2025-06-26 11:08:57.049 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(13)自动调整最大可见行数为: 13
2025-06-26 11:08:57.049 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 13 行, 16 列
2025-06-26 11:08:57.049 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 13 行, 最大可见行数: 13, 耗时: 0.00ms
2025-06-26 11:08:57.049 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 13
2025-06-26 11:09:03.380 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2026年 > 1月', '工资表 > 2026年', '工资表', '异动人员表']
2025-06-26 11:09:03.380 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 1月 > 全部在职人员
2025-06-26 11:09:03.380 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 11:09:03.380 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2757 | 数据量大(1396条)，启用分页模式
2025-06-26 11:09:03.380 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2785 | 使用分页模式加载 salary_data_2026_01_active_employees，第1页，每页50条
2025-06-26 11:09:03.395 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2828 | 缓存未命中，从数据库加载: salary_data_2026_01_active_employees 第1页
2025-06-26 11:09:03.395 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 1月 > 全部在职人员
2025-06-26 11:09:03.395 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2026_01_active_employees 第1页数据，每页50条
2025-06-26 11:09:03.395 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_01_active_employees 分页获取数据: 第1页, 每页50条
2025-06-26 11:09:03.395 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_01_active_employees 获取第1页数据: 50 行，总计1396行
2025-06-26 11:09:03.395 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 11:09:03.395 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2643 | 表 salary_data_2026_01_active_employees 没有保存的字段映射信息
2025-06-26 11:09:03.395 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 1396
2025-06-26 11:09:03.395 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2858 | 分页数据加载成功（数据库）: 50条数据，第1页，总计1396条
2025-06-26 11:09:03.411 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_01_active_employees 分页获取数据: 第2页, 每页50条
2025-06-26 11:09:03.411 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 11:09:03.411 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2702 | 表 salary_data_2026_01_active_employees 无字段偏好设置，显示所有字段
2025-06-26 11:09:03.411 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-26 11:09:03.411 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 13 -> 50
2025-06-26 11:09:03.411 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 11:09:03.411 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_01_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-26 11:09:03.411 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 11:09:03.442 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 30.99ms
2025-06-26 11:09:03.451 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-26 11:09:03.454 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 1396
2025-06-26 11:09:13.277 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-26 11:09:13.277 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 11:09:13.277 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 11:09:13.277 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_01_active_employees 分页获取数据: 第2页, 每页50条
2025-06-26 11:09:13.277 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_01_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-26 11:09:13.277 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 50
2025-06-26 11:09:13.277 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 11:09:13.277 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 11:09:18.907 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 5630.29ms
2025-06-26 11:09:18.907 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第2页, 显示50条记录，字段数: 16
2025-06-26 11:09:18.923 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 2
2025-06-26 11:09:25.538 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-26 11:09:25.539 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 11:09:25.540 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 11:09:25.540 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_01_active_employees 分页获取数据: 第1页, 每页50条
2025-06-26 11:09:25.543 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_01_active_employees 获取第1页数据: 50 行，总计1396行
2025-06-26 11:09:25.545 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 50
2025-06-26 11:09:25.546 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 11:09:25.547 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 11:09:31.359 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 5814.13ms
2025-06-26 11:09:31.359 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第1页, 显示50条记录，字段数: 16
2025-06-26 11:09:31.359 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 1
2025-06-26 11:09:39.980 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2026年 > 1月', '工资表 > 2026年', '工资表', '异动人员表']
2025-06-26 11:09:39.980 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 4月
2025-06-26 11:09:39.980 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 11:09:39.995 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 50
2025-06-26 11:09:39.995 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 11:09:40.011 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 50, 耗时: 15.68ms
2025-06-26 11:09:40.011 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 11:09:40.011 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 11:09:40.011 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 11:09:40.011 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 50
2025-06-26 11:09:40.011 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 11:09:40.011 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 50, 耗时: 0.00ms
2025-06-26 11:09:40.011 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 11:09:40.011 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 11:09:40.011 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 4月
2025-06-26 11:09:42.287 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2026年 > 4月', '工资表 > 2026年 > 1月', '工资表 > 2026年', '工资表']
2025-06-26 11:09:42.287 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 4月 > A岗职工
2025-06-26 11:09:42.287 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 11:09:42.287 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2757 | 数据量大(62条)，启用分页模式
2025-06-26 11:09:42.287 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2785 | 使用分页模式加载 salary_data_2026_04_a_grade_employees，第1页，每页50条
2025-06-26 11:09:42.287 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2828 | 缓存未命中，从数据库加载: salary_data_2026_04_a_grade_employees 第1页
2025-06-26 11:09:42.287 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 4月 > A岗职工
2025-06-26 11:09:42.287 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2026_04_a_grade_employees 第1页数据，每页50条
2025-06-26 11:09:42.287 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_04_a_grade_employees 分页获取数据: 第1页, 每页50条
2025-06-26 11:09:42.302 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_04_a_grade_employees 获取第1页数据: 50 行，总计62行
2025-06-26 11:09:42.302 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 11:09:42.302 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2662 | 应用字段映射到表 salary_data_2026_04_a_grade_employees: 7 个字段重命名
2025-06-26 11:09:42.302 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 62
2025-06-26 11:09:42.302 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2858 | 分页数据加载成功（数据库）: 50条数据，第1页，总计62条
2025-06-26 11:09:42.318 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_04_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-26 11:09:42.318 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 11:09:42.318 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2696 | 应用表 salary_data_2026_04_a_grade_employees 的字段偏好: 2个字段 ['id_card', 'basic_salary']
2025-06-26 11:09:42.318 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-26 11:09:42.318 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 50
2025-06-26 11:09:42.318 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 11:09:42.318 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_04_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-26 11:09:42.318 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 2 个表头
2025-06-26 11:09:42.335 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 2 列
2025-06-26 11:09:42.348 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 30.17ms
2025-06-26 11:09:42.350 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-26 11:09:42.353 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 62
2025-06-26 11:09:48.551 | INFO     | src.gui.prototype.prototype_main_window:_on_refresh_data:420 | 刷新数据功能被触发，将强制刷新导航面板。
2025-06-26 11:09:48.551 | WARNING  | src.gui.prototype.prototype_main_window:_on_refresh_data:435 | 导航面板不支持强制刷新。
2025-06-26 11:09:56.487 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-26 11:09:56.487 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 11:09:56.487 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 11:09:56.487 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_04_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-26 11:09:56.487 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_04_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-26 11:09:56.487 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 12
2025-06-26 11:09:56.487 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(12)自动调整最大可见行数为: 12
2025-06-26 11:09:56.487 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 11:09:56.487 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 12 行, 16 列
2025-06-26 11:09:56.518 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 12 行, 最大可见行数: 12, 耗时: 30.87ms
2025-06-26 11:09:56.518 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第2页, 显示12条记录，字段数: 16
2025-06-26 11:09:56.518 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 2
2025-06-26 11:10:14.162 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-26 11:10:14.162 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 11:10:14.163 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 11:10:14.163 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_04_a_grade_employees 分页获取数据: 第1页, 每页50条
2025-06-26 11:10:14.166 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_04_a_grade_employees 获取第1页数据: 50 行，总计62行
2025-06-26 11:10:14.169 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 12 -> 50
2025-06-26 11:10:14.169 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 11:10:14.169 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 11:10:14.170 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 11:10:14.177 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 7.99ms
2025-06-26 11:10:14.177 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第1页, 显示50条记录，字段数: 16
2025-06-26 11:10:14.177 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 1
2025-06-26 11:10:40.033 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2026年 > 4月', '工资表 > 2026年 > 1月', '工资表 > 2026年', '工资表']
2025-06-26 11:10:40.033 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 4月 > 全部在职人员
2025-06-26 11:10:40.033 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 11:10:40.033 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2757 | 数据量大(1396条)，启用分页模式
2025-06-26 11:10:40.033 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2785 | 使用分页模式加载 salary_data_2026_04_active_employees，第1页，每页50条
2025-06-26 11:10:40.033 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2828 | 缓存未命中，从数据库加载: salary_data_2026_04_active_employees 第1页
2025-06-26 11:10:40.033 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 4月 > 全部在职人员
2025-06-26 11:10:40.033 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2026_04_active_employees 第1页数据，每页50条
2025-06-26 11:10:40.033 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_04_active_employees 分页获取数据: 第1页, 每页50条
2025-06-26 11:10:40.050 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_04_active_employees 获取第1页数据: 50 行，总计1396行
2025-06-26 11:10:40.050 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 11:10:40.050 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2662 | 应用字段映射到表 salary_data_2026_04_active_employees: 7 个字段重命名
2025-06-26 11:10:40.050 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 1396
2025-06-26 11:10:40.050 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2858 | 分页数据加载成功（数据库）: 50条数据，第1页，总计1396条
2025-06-26 11:10:40.064 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_04_active_employees 分页获取数据: 第2页, 每页50条
2025-06-26 11:10:40.064 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 11:10:40.064 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2702 | 表 salary_data_2026_04_active_employees 无字段偏好设置，显示所有字段
2025-06-26 11:10:40.064 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-26 11:10:40.064 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 50
2025-06-26 11:10:40.064 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 11:10:40.064 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_04_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-26 11:10:40.064 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 11:10:40.079 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 11:10:40.103 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 39.45ms
2025-06-26 11:10:40.115 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-26 11:10:40.116 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 1396
2025-06-26 11:10:48.308 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-26 11:10:48.308 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 11:10:48.323 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 11:10:48.323 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_04_active_employees 分页获取数据: 第2页, 每页50条
2025-06-26 11:10:48.323 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_04_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-26 11:10:48.323 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 50
2025-06-26 11:10:48.323 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 11:10:48.323 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 11:10:48.323 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 11:10:53.372 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 5048.22ms
2025-06-26 11:10:53.372 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第2页, 显示50条记录，字段数: 16
2025-06-26 11:10:53.372 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 2
2025-06-26 11:11:04.259 | INFO     | __main__:main:302 | 应用程序正常退出
2025-06-26 11:37:18.398 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-26 11:37:18.399 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-26 11:37:18.399 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-26 11:37:18.399 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-26 11:37:18.400 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-26 11:37:18.400 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-26 11:37:19.226 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 11:37:19.226 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 11:38:36.672 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-26 11:38:36.673 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-26 11:38:36.673 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-26 11:38:36.673 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-26 11:38:36.674 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-26 11:38:36.674 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-26 11:38:37.482 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 11:40:04.157 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-26 11:40:04.158 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-26 11:40:04.158 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-26 11:40:04.158 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-26 11:40:04.159 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-26 11:40:04.159 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-26 11:40:04.958 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 11:40:04.959 | INFO     | src.modules.system_config.config_manager:load_config:338 | 正在加载配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 11:40:04.960 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:252 | 配置文件验证通过
2025-06-26 11:40:04.960 | INFO     | src.modules.system_config.config_manager:load_config:350 | 配置文件加载成功
2025-06-26 11:40:04.961 | INFO     | src.modules.data_storage.database_manager:_initialize_database:101 | 开始初始化数据库...
2025-06-26 11:40:04.970 | INFO     | src.modules.data_storage.database_manager:_initialize_database:156 | 数据库初始化完成
2025-06-26 11:40:04.971 | INFO     | src.modules.data_storage.database_manager:__init__:97 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-06-26 11:40:04.972 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 11:40:04.973 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:102 | 动态表管理器初始化完成
2025-06-26 11:40:04.989 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_01_a_grade_employees 分页获取数据: 第1页, 每页50条
2025-06-26 11:40:04.994 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_01_a_grade_employees 获取第1页数据: 50 行，总计62行
2025-06-26 11:40:04.995 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_01_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-26 11:40:04.998 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_01_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-26 11:40:05.005 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_04_a_grade_employees 分页获取数据: 第1页, 每页50条
2025-06-26 11:40:05.010 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_04_a_grade_employees 获取第1页数据: 50 行，总计62行
2025-06-26 11:40:05.010 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_04_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-26 11:40:05.010 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_04_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-26 11:41:44.806 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-26 11:41:44.806 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-26 11:41:44.807 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-26 11:41:44.807 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-26 11:41:44.807 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-26 11:41:44.808 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-26 11:41:45.609 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 11:41:45.610 | INFO     | src.modules.system_config.config_manager:load_config:338 | 正在加载配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 11:41:45.610 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:252 | 配置文件验证通过
2025-06-26 11:41:45.611 | INFO     | src.modules.system_config.config_manager:load_config:350 | 配置文件加载成功
2025-06-26 11:41:45.612 | INFO     | src.modules.data_storage.database_manager:_initialize_database:101 | 开始初始化数据库...
2025-06-26 11:41:45.623 | INFO     | src.modules.data_storage.database_manager:_initialize_database:156 | 数据库初始化完成
2025-06-26 11:41:45.623 | INFO     | src.modules.data_storage.database_manager:__init__:97 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-06-26 11:41:45.624 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 11:41:45.625 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:102 | 动态表管理器初始化完成
2025-06-26 11:41:45.628 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_01_a_grade_employees 分页获取数据: 第1页, 每页50条
2025-06-26 11:41:45.634 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_01_a_grade_employees 获取第1页数据: 50 行，总计62行
2025-06-26 11:41:45.634 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_01_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-26 11:41:45.638 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_01_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-26 11:41:45.650 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_04_a_grade_employees 分页获取数据: 第1页, 每页50条
2025-06-26 11:41:45.650 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_04_a_grade_employees 获取第1页数据: 50 行，总计62行
2025-06-26 11:41:45.650 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_04_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-26 11:41:45.650 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_04_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-26 11:45:21.785 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-26 11:45:21.785 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-26 11:45:21.786 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-26 11:45:21.786 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-26 11:45:21.786 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-26 11:45:21.788 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-26 11:45:23.017 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 11:45:23.018 | INFO     | src.modules.system_config.config_manager:load_config:338 | 正在加载配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 11:45:23.018 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:252 | 配置文件验证通过
2025-06-26 11:45:23.019 | INFO     | src.modules.system_config.config_manager:load_config:350 | 配置文件加载成功
2025-06-26 11:45:23.019 | INFO     | src.modules.data_storage.database_manager:_initialize_database:101 | 开始初始化数据库...
2025-06-26 11:45:23.040 | INFO     | src.modules.data_storage.database_manager:_initialize_database:156 | 数据库初始化完成
2025-06-26 11:45:23.042 | INFO     | src.modules.data_storage.database_manager:__init__:97 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-06-26 11:45:23.043 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 11:45:23.043 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:102 | 动态表管理器初始化完成
2025-06-26 11:45:23.044 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_04_a_grade_employees 分页获取数据: 第1页, 每页1条
2025-06-26 11:45:23.048 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_04_a_grade_employees 获取第1页数据: 1 行，总计62行
2025-06-26 11:45:23.048 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 11:45:23.380 | INFO     | src.gui.table_field_preference_dialog:load_current_preference:279 | 加载表 salary_data_2026_04_a_grade_employees 的偏好设置: 7 个字段
2025-06-26 11:45:23.381 | INFO     | src.gui.table_field_preference_dialog:__init__:74 | 表级字段偏好对话框初始化完成: salary_data_2026_04_a_grade_employees
2025-06-26 13:39:11.030 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-26 13:39:11.031 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-26 13:39:11.031 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-26 13:39:11.031 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-26 13:39:11.032 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-26 13:39:11.033 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-26 13:39:11.348 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 13:39:11.349 | INFO     | src.modules.system_config.config_manager:load_config:338 | 正在加载配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 13:39:11.350 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:252 | 配置文件验证通过
2025-06-26 13:39:11.350 | INFO     | src.modules.system_config.config_manager:load_config:350 | 配置文件加载成功
2025-06-26 13:39:11.351 | INFO     | src.modules.data_storage.database_manager:_initialize_database:101 | 开始初始化数据库...
2025-06-26 13:39:11.362 | INFO     | src.modules.data_storage.database_manager:_initialize_database:156 | 数据库初始化完成
2025-06-26 13:39:11.363 | INFO     | src.modules.data_storage.database_manager:__init__:97 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-06-26 13:39:11.364 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 13:39:11.364 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:102 | 动态表管理器初始化完成
2025-06-26 13:39:11.383 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_01_a_grade_employees 分页获取数据: 第1页, 每页20条
2025-06-26 13:39:11.386 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_01_a_grade_employees 获取第1页数据: 20 行，总计62行
2025-06-26 13:39:11.391 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_01_a_grade_employees 分页获取数据: 第2页, 每页20条
2025-06-26 13:39:11.407 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_01_a_grade_employees 获取第2页数据: 20 行，总计62行
2025-06-26 13:39:11.408 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_01_a_grade_employees 分页获取数据: 第3页, 每页20条
2025-06-26 13:39:11.411 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_01_a_grade_employees 获取第3页数据: 20 行，总计62行
2025-06-26 13:39:11.412 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_04_a_grade_employees 分页获取数据: 第1页, 每页20条
2025-06-26 13:39:11.412 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_04_a_grade_employees 获取第1页数据: 20 行，总计62行
2025-06-26 13:39:11.428 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_04_a_grade_employees 分页获取数据: 第2页, 每页20条
2025-06-26 13:39:11.533 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_04_a_grade_employees 获取第2页数据: 20 行，总计62行
2025-06-26 13:39:11.605 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_04_a_grade_employees 分页获取数据: 第3页, 每页20条
2025-06-26 13:39:11.609 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_04_a_grade_employees 获取第3页数据: 20 行，总计62行
2025-06-26 13:39:11.630 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_04_a_grade_employees 分页获取数据: 第1页, 每页10条
2025-06-26 13:39:11.646 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_04_a_grade_employees 获取第1页数据: 10 行，总计62行
2025-06-26 13:39:11.646 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_04_a_grade_employees 分页获取数据: 第1页, 每页10条
2025-06-26 13:39:11.646 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_04_a_grade_employees 获取第1页数据: 10 行，总计62行
2025-06-26 13:39:11.669 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_01_a_grade_employees 分页获取数据: 第1页, 每页25条
2025-06-26 13:39:11.669 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_01_a_grade_employees 获取第1页数据: 25 行，总计62行
2025-06-26 13:39:11.669 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_01_a_grade_employees 分页获取数据: 第2页, 每页25条
2025-06-26 13:39:11.669 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_01_a_grade_employees 获取第2页数据: 25 行，总计62行
2025-06-26 13:39:11.685 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_04_a_grade_employees 分页获取数据: 第1页, 每页25条
2025-06-26 13:39:11.685 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_04_a_grade_employees 获取第1页数据: 25 行，总计62行
2025-06-26 13:39:11.685 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_04_a_grade_employees 分页获取数据: 第2页, 每页25条
2025-06-26 13:39:11.701 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_04_a_grade_employees 获取第2页数据: 25 行，总计62行
2025-06-26 13:41:56.965 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-26 13:41:56.965 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-26 13:41:56.965 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-26 13:41:56.965 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-26 13:41:56.965 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-26 13:41:56.965 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-26 13:41:57.773 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 13:41:57.773 | INFO     | src.modules.system_config.config_manager:load_config:338 | 正在加载配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 13:41:57.774 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:252 | 配置文件验证通过
2025-06-26 13:41:57.774 | INFO     | src.modules.system_config.config_manager:load_config:350 | 配置文件加载成功
2025-06-26 13:41:57.776 | INFO     | src.modules.data_storage.database_manager:_initialize_database:101 | 开始初始化数据库...
2025-06-26 13:41:57.789 | INFO     | src.modules.data_storage.database_manager:_initialize_database:156 | 数据库初始化完成
2025-06-26 13:41:57.790 | INFO     | src.modules.data_storage.database_manager:__init__:97 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-06-26 13:41:57.791 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 13:41:57.791 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:102 | 动态表管理器初始化完成
2025-06-26 13:41:57.796 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_01_a_grade_employees 分页获取数据: 第1页, 每页1条
2025-06-26 13:41:57.799 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_01_a_grade_employees 获取第1页数据: 1 行，总计62行
2025-06-26 13:41:57.825 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_01_a_grade_employees 分页获取数据: 第1页, 每页20条
2025-06-26 13:41:57.829 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_01_a_grade_employees 获取第1页数据: 20 行，总计62行
2025-06-26 13:41:57.871 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_01_a_grade_employees 分页获取数据: 第2页, 每页20条
2025-06-26 13:41:57.890 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_01_a_grade_employees 获取第2页数据: 20 行，总计62行
2025-06-26 13:41:57.922 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_01_a_grade_employees 分页获取数据: 第3页, 每页20条
2025-06-26 13:41:57.922 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_01_a_grade_employees 获取第3页数据: 20 行，总计62行
2025-06-26 13:41:57.922 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_01_a_grade_employees 分页获取数据: 第4页, 每页20条
2025-06-26 13:41:57.922 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_01_a_grade_employees 获取第4页数据: 2 行，总计62行
2025-06-26 13:41:57.922 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_04_a_grade_employees 分页获取数据: 第1页, 每页1条
2025-06-26 13:41:57.922 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_04_a_grade_employees 获取第1页数据: 1 行，总计62行
2025-06-26 13:41:57.964 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_04_a_grade_employees 分页获取数据: 第1页, 每页20条
2025-06-26 13:41:57.964 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_04_a_grade_employees 获取第1页数据: 20 行，总计62行
2025-06-26 13:41:57.994 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_04_a_grade_employees 分页获取数据: 第2页, 每页20条
2025-06-26 13:41:57.995 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_04_a_grade_employees 获取第2页数据: 20 行，总计62行
2025-06-26 13:41:57.996 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_04_a_grade_employees 分页获取数据: 第3页, 每页20条
2025-06-26 13:41:57.996 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_04_a_grade_employees 获取第3页数据: 20 行，总计62行
2025-06-26 13:41:57.996 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_04_a_grade_employees 分页获取数据: 第4页, 每页20条
2025-06-26 13:41:58.009 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_04_a_grade_employees 获取第4页数据: 2 行，总计62行
2025-06-26 14:18:39.450 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-26 14:18:39.450 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-26 14:18:39.450 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-26 14:18:39.450 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-26 14:18:39.450 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-26 14:18:39.450 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-26 14:18:40.764 | INFO     | __main__:setup_app_logging:206 | 月度工资异动处理系统 v2.0.0-refactored 启动
2025-06-26 14:18:40.764 | INFO     | __main__:main:270 | 初始化核心管理器...
2025-06-26 14:18:40.764 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 14:18:40.764 | INFO     | src.modules.system_config.config_manager:load_config:338 | 正在加载配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 14:18:40.764 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:252 | 配置文件验证通过
2025-06-26 14:18:40.764 | INFO     | src.modules.system_config.config_manager:load_config:350 | 配置文件加载成功
2025-06-26 14:18:40.764 | INFO     | src.modules.data_storage.database_manager:_initialize_database:101 | 开始初始化数据库...
2025-06-26 14:18:40.786 | INFO     | src.modules.data_storage.database_manager:_initialize_database:156 | 数据库初始化完成
2025-06-26 14:18:40.787 | INFO     | src.modules.data_storage.database_manager:__init__:97 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-06-26 14:18:40.788 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 14:18:40.788 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:102 | 动态表管理器初始化完成
2025-06-26 14:18:40.789 | INFO     | __main__:main:275 | 核心管理器初始化完成。
2025-06-26 14:18:40.789 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成 - 最大条目数: 50, 最大内存: 50MB
2025-06-26 14:18:40.797 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-06-26 14:18:40.799 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-06-26 14:18:41.097 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:1493 | 菜单栏创建完成
2025-06-26 14:18:41.097 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 14:18:41.097 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设置: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 14:18:41.097 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成功
2025-06-26 14:18:41.097 | INFO     | src.gui.prototype.prototype_main_window:__init__:1469 | 菜单栏管理器初始化完成
2025-06-26 14:18:41.097 | INFO     | src.gui.table_header_manager:__init__:68 | 表头管理器初始化完成
2025-06-26 14:18:41.097 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:2170 | 管理器设置完成，包含增强版表头管理器
2025-06-26 14:18:41.097 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_state:151 | 导航状态加载成功
2025-06-26 14:18:41.097 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-06-26 14:18:41.128 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:_load_data:556 | 数据加载成功: state/user/tree_expansion_data.json
2025-06-26 14:18:41.128 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:148 | 智能树形展开算法初始化完成
2025-06-26 14:18:41.128 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:916 | 开始从元数据动态加载工资数据...
2025-06-26 14:18:41.128 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:923 | 检测到首次启动，将延迟加载导航数据确保数据库就绪
2025-06-26 14:18:41.141 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:654 | 导航面板已重构：移除功能性导航，专注数据导航
2025-06-26 14:18:41.161 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:689 | 恢复导航状态: 90个展开项
2025-06-26 14:18:41.197 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '异动人员表 > 2025年 > 5月', '工资表', '异动人员表', '异动人员表 > 2025年 > 4月']
2025-06-26 14:18:41.199 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:495 | 增强导航面板初始化完成
2025-06-26 14:18:41.229 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1264 | 快捷键注册完成: 18/18 个
2025-06-26 14:18:41.230 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1475 | 拖拽排序管理器初始化完成
2025-06-26 14:18:41.232 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 14:18:41.232 | INFO     | src.modules.data_import.header_edit_manager:__init__:74 | 表头编辑管理器初始化完成
2025-06-26 14:18:41.234 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1851 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-06-26 14:18:41.235 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 14:18:41.236 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 14:18:41.285 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 49.86ms
2025-06-26 14:18:41.290 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 14:18:41.296 | INFO     | src.gui.widgets.pagination_widget:__init__:88 | 分页组件初始化完成
2025-06-26 14:18:41.367 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:379 | 控制面板按钮信号连接完成
2025-06-26 14:18:41.378 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 14:18:41.517 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 14:18:41.518 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:2143 | 快捷键设置完成
2025-06-26 14:18:41.519 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:2132 | 主窗口UI设置完成。
2025-06-26 14:18:41.522 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:2198 | 信号连接设置完成
2025-06-26 14:18:41.524 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 14:18:41.659 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:2627 | 已加载字段映射信息，共44个表的映射
2025-06-26 14:18:41.659 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 14:18:41.660 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 14:18:41.661 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 14:18:41.662 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 2.02ms
2025-06-26 14:18:41.663 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 14:18:41.663 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 14:18:41.664 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 14:18:41.664 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 14:18:41.665 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 14:18:41.665 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 1.54ms
2025-06-26 14:18:41.666 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 14:18:41.666 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 14:18:41.667 | INFO     | src.gui.prototype.prototype_main_window:__init__:2084 | 原型主窗口初始化完成
2025-06-26 14:18:42.057 | INFO     | __main__:main:297 | 应用程序启动成功
2025-06-26 14:18:42.062 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1266px)
2025-06-26 14:18:42.063 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:1241 | MainWorkspaceArea 响应式适配: sm
2025-06-26 14:18:42.076 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_load_salary_data:938 | 执行延迟的工资数据加载...
2025-06-26 14:18:42.076 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1036 | 开始强制刷新工资数据导航... (尝试 1/3)
2025-06-26 14:18:42.076 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1044 | 找到工资表节点: 📊 工资表
2025-06-26 14:18:42.098 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:702 | 找到 68 个匹配类型 'salary_data' 的表
2025-06-26 14:18:42.129 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2026年，包含 5 个月份
2025-06-26 14:18:42.130 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2025年，包含 3 个月份
2025-06-26 14:18:42.209 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2024年，包含 1 个月份
2025-06-26 14:18:42.209 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2023年，包含 1 个月份
2025-06-26 14:18:42.209 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2022年，包含 1 个月份
2025-06-26 14:18:42.209 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2021年，包含 1 个月份
2025-06-26 14:18:42.209 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2020年，包含 1 个月份
2025-06-26 14:18:42.209 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2019年，包含 1 个月份
2025-06-26 14:18:42.209 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2018年，包含 1 个月份
2025-06-26 14:18:42.209 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2017年，包含 1 个月份
2025-06-26 14:18:42.209 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2016年，包含 1 个月份
2025-06-26 14:18:42.209 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1149 | 工资数据导航已强制刷新: 11 个年份, 17 个月份
2025-06-26 14:18:42.209 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1152 | 默认路径: 工资表 > 2026年 > 1月 > 全部在职人员
2025-06-26 14:18:42.209 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1191 | force_refresh_salary_data 执行完成
2025-06-26 14:18:58.321 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表', '异动人员表', '异动人员表 > 2025年 > 4月', '异动人员表 > 2025年']
2025-06-26 14:18:58.321 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年
2025-06-26 14:18:58.321 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 14:18:58.321 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 14:18:58.321 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 14:18:58.321 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 0.00ms
2025-06-26 14:18:58.321 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 14:18:58.321 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 14:18:58.321 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 14:18:58.321 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 14:18:58.321 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 14:18:58.321 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 0.00ms
2025-06-26 14:18:58.321 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 14:18:58.321 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 14:18:58.321 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年
2025-06-26 14:19:02.168 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2026年', '工资表', '异动人员表', '异动人员表 > 2025年 > 4月']
2025-06-26 14:19:02.168 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 4月
2025-06-26 14:19:02.168 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 14:19:02.168 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 14:19:02.168 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 14:19:02.168 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 0.00ms
2025-06-26 14:19:02.168 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 14:19:02.184 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 14:19:02.184 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 14:19:02.184 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 14:19:02.184 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 14:19:02.184 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 0.00ms
2025-06-26 14:19:02.184 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 14:19:02.184 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 14:19:02.184 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 4月
2025-06-26 14:19:04.804 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2026年 > 4月', '工资表 > 2026年', '工资表', '异动人员表']
2025-06-26 14:19:04.804 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 4月 > A岗职工
2025-06-26 14:19:04.804 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 14:19:04.804 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2757 | 数据量大(62条)，启用分页模式
2025-06-26 14:19:04.804 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2785 | 使用分页模式加载 salary_data_2026_04_a_grade_employees，第1页，每页50条
2025-06-26 14:19:04.804 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2828 | 缓存未命中，从数据库加载: salary_data_2026_04_a_grade_employees 第1页
2025-06-26 14:19:04.804 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 4月 > A岗职工
2025-06-26 14:19:04.804 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2026_04_a_grade_employees 第1页数据，每页50条
2025-06-26 14:19:04.804 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_04_a_grade_employees 分页获取数据: 第1页, 每页50条
2025-06-26 14:19:04.819 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_04_a_grade_employees 获取第1页数据: 50 行，总计62行
2025-06-26 14:19:04.819 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 14:19:04.819 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2662 | 应用字段映射到表 salary_data_2026_04_a_grade_employees: 7 个字段重命名
2025-06-26 14:19:04.819 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 62
2025-06-26 14:19:04.819 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2858 | 分页数据加载成功（数据库）: 50条数据，第1页，总计62条
2025-06-26 14:19:04.819 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_04_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-26 14:19:04.819 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 14:19:04.819 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2696 | 应用表 salary_data_2026_04_a_grade_employees 的字段偏好: 2个字段 ['id_card', 'basic_salary']
2025-06-26 14:19:04.819 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-26 14:19:04.819 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 1000 -> 50
2025-06-26 14:19:04.819 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_04_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-26 14:19:04.819 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 14:19:04.835 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 2 个表头
2025-06-26 14:19:04.835 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 2 列
2025-06-26 14:19:04.835 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 15.86ms
2025-06-26 14:19:04.835 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-26 14:19:04.835 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 62
2025-06-26 14:19:07.835 | INFO     | src.gui.prototype.prototype_main_window:_on_refresh_data:420 | 刷新数据功能被触发，将强制刷新导航面板。
2025-06-26 14:19:07.835 | WARNING  | src.gui.prototype.prototype_main_window:_on_refresh_data:435 | 导航面板不支持强制刷新。
2025-06-26 14:19:18.000 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-26 14:19:18.000 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 14:19:18.000 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 14:19:18.000 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_04_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-26 14:19:18.000 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_04_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-26 14:19:18.016 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 12
2025-06-26 14:19:18.016 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(12)自动调整最大可见行数为: 12
2025-06-26 14:19:18.016 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 14:19:18.016 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 12 行, 16 列
2025-06-26 14:19:18.038 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 12 行, 最大可见行数: 12, 耗时: 21.91ms
2025-06-26 14:19:18.039 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第2页, 显示12条记录，字段数: 16
2025-06-26 14:19:18.039 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 2
2025-06-26 14:19:27.821 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-26 14:19:27.821 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 14:19:27.821 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 14:19:27.821 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_04_a_grade_employees 分页获取数据: 第1页, 每页50条
2025-06-26 14:19:27.821 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_04_a_grade_employees 获取第1页数据: 50 行，总计62行
2025-06-26 14:19:27.821 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 12 -> 50
2025-06-26 14:19:27.821 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 14:19:27.821 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 14:19:27.821 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 14:19:27.837 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 16.15ms
2025-06-26 14:19:27.837 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第1页, 显示50条记录，字段数: 16
2025-06-26 14:19:27.837 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 1
2025-06-26 14:19:34.530 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2026年 > 4月', '工资表 > 2026年', '工资表', '异动人员表']
2025-06-26 14:19:34.531 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 4月 > 全部在职人员
2025-06-26 14:19:34.531 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 14:19:34.533 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2757 | 数据量大(1396条)，启用分页模式
2025-06-26 14:19:34.533 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2785 | 使用分页模式加载 salary_data_2026_04_active_employees，第1页，每页50条
2025-06-26 14:19:34.534 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2828 | 缓存未命中，从数据库加载: salary_data_2026_04_active_employees 第1页
2025-06-26 14:19:34.534 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 4月 > 全部在职人员
2025-06-26 14:19:34.535 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2026_04_active_employees 第1页数据，每页50条
2025-06-26 14:19:34.535 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_04_active_employees 分页获取数据: 第1页, 每页50条
2025-06-26 14:19:34.539 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_04_active_employees 获取第1页数据: 50 行，总计1396行
2025-06-26 14:19:34.539 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 14:19:34.539 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2662 | 应用字段映射到表 salary_data_2026_04_active_employees: 7 个字段重命名
2025-06-26 14:19:34.540 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 1396
2025-06-26 14:19:34.549 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2858 | 分页数据加载成功（数据库）: 50条数据，第1页，总计1396条
2025-06-26 14:19:34.558 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_04_active_employees 分页获取数据: 第2页, 每页50条
2025-06-26 14:19:34.558 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 14:19:34.561 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2702 | 表 salary_data_2026_04_active_employees 无字段偏好设置，显示所有字段
2025-06-26 14:19:34.566 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_04_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-26 14:19:34.569 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-26 14:19:34.585 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 50
2025-06-26 14:19:34.595 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 14:19:34.609 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 14:19:34.617 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 14:19:34.634 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 49.22ms
2025-06-26 14:19:34.635 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-26 14:19:34.636 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 1396
2025-06-26 14:19:53.872 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-26 14:19:53.872 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 14:19:53.872 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 14:19:53.872 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_04_active_employees 分页获取数据: 第2页, 每页50条
2025-06-26 14:19:53.872 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_04_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-26 14:19:53.872 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 50
2025-06-26 14:19:53.872 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 14:19:53.872 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 14:19:53.872 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 14:19:58.007 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 4135.25ms
2025-06-26 14:19:58.007 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第2页, 显示50条记录，字段数: 16
2025-06-26 14:19:58.007 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 2
2025-06-26 14:20:09.030 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-26 14:20:09.030 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 14:20:09.030 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 14:20:09.030 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_04_active_employees 分页获取数据: 第3页, 每页50条
2025-06-26 14:20:09.046 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_04_active_employees 获取第3页数据: 50 行，总计1396行
2025-06-26 14:20:09.046 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 50
2025-06-26 14:20:09.046 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 14:20:09.046 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 14:20:09.046 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 14:20:12.478 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 3432.00ms
2025-06-26 14:20:12.478 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第3页, 显示50条记录，字段数: 16
2025-06-26 14:20:12.478 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 3
2025-06-26 14:20:24.181 | INFO     | src.gui.prototype.prototype_main_window:_on_menu_action_triggered:2295 | 菜单动作触发: customize_headers
2025-06-26 14:20:24.182 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 14:20:24.184 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 14:20:24.186 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 14:20:24.197 | INFO     | src.gui.table_field_preference_dialog:load_current_preference:283 | 表 salary_data_2026_04_active_employees 无偏好设置，使用默认选择
2025-06-26 14:20:24.198 | INFO     | src.gui.table_field_preference_dialog:__init__:74 | 表级字段偏好对话框初始化完成: salary_data_2026_04_active_employees
2025-06-26 14:21:01.100 | INFO     | src.modules.data_import.config_sync_manager:save_table_field_preference:758 | 表级字段偏好保存成功: salary_data_2026_04_active_employees, 7 个字段
2025-06-26 14:21:01.116 | INFO     | src.gui.table_field_preference_dialog:save_preference:385 | 保存表级字段偏好成功: salary_data_2026_04_active_employees, 7 个字段
2025-06-26 14:21:01.116 | INFO     | src.gui.prototype.prototype_main_window:_on_table_field_preference_saved:3465 | 表级字段偏好已保存: salary_data_2026_04_active_employees, 7 个字段
2025-06-26 14:21:01.116 | INFO     | src.gui.prototype.prototype_main_window:_reload_current_table_data:3624 | 重新加载表格数据: salary_data_2026_04_active_employees
2025-06-26 14:21:01.116 | ERROR    | src.gui.prototype.prototype_main_window:_reload_current_table_data:3642 | 重新加载当前表格数据失败: 'PaginationWidget' object has no attribute 'get_current_page'
2025-06-26 14:21:06.230 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2026年 > 4月 > A岗职工', '工资表 > 2026年 > 4月', '工资表 > 2026年', '工资表']
2025-06-26 14:21:06.245 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 4月 > A岗职工
2025-06-26 14:21:06.245 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 14:21:06.245 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2757 | 数据量大(62条)，启用分页模式
2025-06-26 14:21:06.245 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2785 | 使用分页模式加载 salary_data_2026_04_a_grade_employees，第1页，每页50条
2025-06-26 14:21:06.245 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2794 | 缓存命中: salary_data_2026_04_a_grade_employees 第1页
2025-06-26 14:21:06.245 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2666 | 表 salary_data_2026_04_a_grade_employees 无需字段重命名
2025-06-26 14:21:06.245 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2858 | 分页数据加载成功（缓存）: 50条数据，第1页，总计62条
2025-06-26 14:21:06.245 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 14:21:06.245 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2696 | 应用表 salary_data_2026_04_a_grade_employees 的字段偏好: 2个字段 ['id_card', 'basic_salary']
2025-06-26 14:21:06.245 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-26 14:21:06.245 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 50
2025-06-26 14:21:06.245 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 14:21:06.245 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 2 个表头
2025-06-26 14:21:06.245 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 2 列
2025-06-26 14:21:06.245 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 0.00ms
2025-06-26 14:21:06.245 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-26 14:21:06.245 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 62
2025-06-26 14:21:06.261 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 4月 > A岗职工
2025-06-26 14:21:10.519 | INFO     | src.gui.prototype.prototype_main_window:_on_refresh_data:420 | 刷新数据功能被触发，将强制刷新导航面板。
2025-06-26 14:21:10.519 | WARNING  | src.gui.prototype.prototype_main_window:_on_refresh_data:435 | 导航面板不支持强制刷新。
2025-06-26 14:21:15.684 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-26 14:21:15.684 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 14:21:15.684 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 14:21:15.684 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_04_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-26 14:21:15.688 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_04_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-26 14:21:15.694 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 12
2025-06-26 14:21:15.695 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(12)自动调整最大可见行数为: 12
2025-06-26 14:21:15.695 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 14:21:15.696 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 12 行, 16 列
2025-06-26 14:21:15.711 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 12 行, 最大可见行数: 12, 耗时: 22.21ms
2025-06-26 14:21:15.712 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第2页, 显示12条记录，字段数: 16
2025-06-26 14:21:15.712 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 2
2025-06-26 14:21:19.133 | INFO     | src.gui.prototype.prototype_main_window:_on_refresh_data:420 | 刷新数据功能被触发，将强制刷新导航面板。
2025-06-26 14:21:19.134 | WARNING  | src.gui.prototype.prototype_main_window:_on_refresh_data:435 | 导航面板不支持强制刷新。
2025-06-26 14:21:45.123 | INFO     | src.modules.data_import.config_sync_manager:update_mapping:222 | 字段 basic_salary 在表 salary_data_2026_04_a_grade_employees 中不存在，创建新字段映射
2025-06-26 14:21:45.123 | INFO     | src.modules.data_import.config_sync_manager:update_mapping:257 | 字段映射更新成功: salary_data_2026_04_a_grade_employees.basic_salary -> 基本工资
2025-06-26 14:21:45.123 | INFO     | src.modules.data_import.header_edit_manager:show_edit_dialog:115 | 字段编辑成功: basic_salary -> 基本工资
2025-06-26 14:21:45.123 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 14:21:45.123 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_refresh_header_display:4008 | 表头显示刷新完成，更新了 1 个表头
2025-06-26 14:21:45.123 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_header_double_clicked:3957 | 表头编辑成功: 列6, basic_salary -> 基本工资
2025-06-26 14:21:52.316 | INFO     | src.gui.prototype.prototype_main_window:_on_refresh_data:420 | 刷新数据功能被触发，将强制刷新导航面板。
2025-06-26 14:21:52.316 | WARNING  | src.gui.prototype.prototype_main_window:_on_refresh_data:435 | 导航面板不支持强制刷新。
2025-06-26 14:21:57.368 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-26 14:21:57.368 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 14:21:57.368 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 14:21:57.368 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_04_a_grade_employees 分页获取数据: 第1页, 每页50条
2025-06-26 14:21:57.368 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_04_a_grade_employees 获取第1页数据: 50 行，总计62行
2025-06-26 14:21:57.386 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 12 -> 50
2025-06-26 14:21:57.386 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 14:21:57.386 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 14:21:57.386 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 14:21:57.386 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 0.00ms
2025-06-26 14:21:57.386 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第1页, 显示50条记录，字段数: 16
2025-06-26 14:21:57.386 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 1
2025-06-26 14:22:00.233 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-26 14:22:00.233 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 14:22:00.233 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 14:22:00.233 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_04_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-26 14:22:00.233 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_04_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-26 14:22:00.310 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 12
2025-06-26 14:22:00.323 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(12)自动调整最大可见行数为: 12
2025-06-26 14:22:00.325 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 14:22:00.325 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 12 行, 16 列
2025-06-26 14:22:00.335 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 12 行, 最大可见行数: 12, 耗时: 101.99ms
2025-06-26 14:22:00.335 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第2页, 显示12条记录，字段数: 16
2025-06-26 14:22:00.335 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 2
2025-06-26 14:22:02.594 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2026年 > 4月 > A岗职工', '工资表 > 2025年', '工资表 > 2026年 > 4月', '工资表 > 2026年', '工资表']
2025-06-26 14:22:02.594 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 4月 > 离休人员
2025-06-26 14:22:02.594 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 14:22:02.594 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2760 | 数据量小(2条)，使用普通模式
2025-06-26 14:22:02.594 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 4月 > 离休人员
2025-06-26 14:22:02.594 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2919 | 统一加载所有字段（修复字段不一致问题）
2025-06-26 14:22:02.594 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2026_04_retired_employees 获取数据...
2025-06-26 14:22:02.594 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2026_04_retired_employees 获取 2 行数据。
2025-06-26 14:22:02.594 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2926 | 成功从数据库加载 2 行数据，16 个字段
2025-06-26 14:22:02.609 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2662 | 应用字段映射到表 salary_data_2026_04_retired_employees: 6 个字段重命名
2025-06-26 14:22:02.609 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2931 | 应用字段映射后的表头: ['序号', '人员代码', '姓名', 'id_card', '部门', 'position', 'basic_salary', 'performance_bonus', 'overtime_pay', '离休\n补贴', 'deduction', '合计', 'month', 'year', 'created_at', 'updated_at']
2025-06-26 14:22:02.609 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 14:22:02.609 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2702 | 表 salary_data_2026_04_retired_employees 无字段偏好设置，显示所有字段
2025-06-26 14:22:02.643 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 2 行
2025-06-26 14:22:02.646 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2583 | 最大可见行数过小可能影响性能，建议至少设置为10，当前值: 2
2025-06-26 14:22:02.674 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 12 -> 2
2025-06-26 14:22:02.701 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(2)自动调整最大可见行数为: 2
2025-06-26 14:22:02.707 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 14:22:02.708 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 2 行, 16 列
2025-06-26 14:22:02.715 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 2 行, 最大可见行数: 2, 耗时: 68.65ms
2025-06-26 14:22:02.715 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 2
2025-06-26 14:22:04.925 | INFO     | src.gui.prototype.prototype_main_window:_on_refresh_data:420 | 刷新数据功能被触发，将强制刷新导航面板。
2025-06-26 14:22:04.925 | WARNING  | src.gui.prototype.prototype_main_window:_on_refresh_data:435 | 导航面板不支持强制刷新。
2025-06-26 14:22:11.032 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2026年 > 4月 > A岗职工', '工资表 > 2025年', '工资表 > 2026年 > 4月', '工资表 > 2026年', '工资表']
2025-06-26 14:22:11.032 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 4月 > 退休人员
2025-06-26 14:22:11.032 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 14:22:11.032 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2760 | 数据量小(13条)，使用普通模式
2025-06-26 14:22:11.032 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 4月 > 退休人员
2025-06-26 14:22:11.032 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2919 | 统一加载所有字段（修复字段不一致问题）
2025-06-26 14:22:11.032 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2026_04_pension_employees 获取数据...
2025-06-26 14:22:11.032 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2026_04_pension_employees 获取 13 行数据。
2025-06-26 14:22:11.032 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2926 | 成功从数据库加载 13 行数据，16 个字段
2025-06-26 14:22:11.032 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2662 | 应用字段映射到表 salary_data_2026_04_pension_employees: 7 个字段重命名
2025-06-26 14:22:11.032 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2931 | 应用字段映射后的表头: ['序号', '人员代码', '姓名', 'id_card', '部门', '人员类别代码', 'basic_salary', 'performance_bonus', 'overtime_pay', '住房补贴', 'deduction', '应发合计', 'month', 'year', 'created_at', 'updated_at']
2025-06-26 14:22:11.032 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 14:22:11.032 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2702 | 表 salary_data_2026_04_pension_employees 无字段偏好设置，显示所有字段
2025-06-26 14:22:11.048 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 13 行
2025-06-26 14:22:11.048 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 2 -> 13
2025-06-26 14:22:11.048 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(13)自动调整最大可见行数为: 13
2025-06-26 14:22:11.048 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 14:22:11.048 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 13 行, 16 列
2025-06-26 14:22:11.048 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 13 行, 最大可见行数: 13, 耗时: 0.00ms
2025-06-26 14:22:11.048 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 13
2025-06-26 14:22:13.386 | INFO     | src.gui.prototype.prototype_main_window:_on_refresh_data:420 | 刷新数据功能被触发，将强制刷新导航面板。
2025-06-26 14:22:13.386 | WARNING  | src.gui.prototype.prototype_main_window:_on_refresh_data:435 | 导航面板不支持强制刷新。
2025-06-26 14:22:17.349 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2026年 > 4月 > A岗职工', '工资表 > 2025年', '工资表 > 2026年 > 4月', '工资表 > 2026年', '工资表']
2025-06-26 14:22:17.349 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 4月 > A岗职工
2025-06-26 14:22:17.349 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 14:22:17.349 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2757 | 数据量大(62条)，启用分页模式
2025-06-26 14:22:17.349 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2785 | 使用分页模式加载 salary_data_2026_04_a_grade_employees，第1页，每页50条
2025-06-26 14:22:17.349 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2794 | 缓存命中: salary_data_2026_04_a_grade_employees 第1页
2025-06-26 14:22:17.349 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2666 | 表 salary_data_2026_04_a_grade_employees 无需字段重命名
2025-06-26 14:22:17.349 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2858 | 分页数据加载成功（缓存）: 50条数据，第1页，总计62条
2025-06-26 14:22:17.349 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 14:22:17.349 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2696 | 应用表 salary_data_2026_04_a_grade_employees 的字段偏好: 2个字段 ['id_card', 'basic_salary']
2025-06-26 14:22:17.349 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-26 14:22:17.349 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 13 -> 50
2025-06-26 14:22:17.349 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 14:22:17.365 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 2 个表头
2025-06-26 14:22:17.365 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 2 列
2025-06-26 14:22:17.365 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 15.65ms
2025-06-26 14:22:17.365 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-26 14:22:17.365 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 62
2025-06-26 14:22:17.365 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 4月 > A岗职工
2025-06-26 14:22:21.280 | INFO     | src.gui.prototype.prototype_main_window:_on_refresh_data:420 | 刷新数据功能被触发，将强制刷新导航面板。
2025-06-26 14:22:21.280 | WARNING  | src.gui.prototype.prototype_main_window:_on_refresh_data:435 | 导航面板不支持强制刷新。
2025-06-26 14:22:30.409 | INFO     | src.gui.prototype.prototype_main_window:_on_menu_action_triggered:2295 | 菜单动作触发: reset_headers
2025-06-26 14:22:31.763 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 14:22:31.780 | INFO     | src.modules.data_import.config_sync_manager:remove_user_header_preference:682 | 用户表头偏好删除成功: salary_data_2026_04_a_grade_employees
2025-06-26 14:22:31.788 | INFO     | src.gui.prototype.prototype_main_window:_reload_current_table_data:3624 | 重新加载表格数据: salary_data_2026_04_a_grade_employees
2025-06-26 14:22:31.800 | ERROR    | src.gui.prototype.prototype_main_window:_reload_current_table_data:3642 | 重新加载当前表格数据失败: 'PaginationWidget' object has no attribute 'get_current_page'
2025-06-26 14:22:31.801 | INFO     | src.gui.prototype.prototype_main_window:_reset_headers_to_default:3524 | 表头已重置为默认显示: salary_data_2026_04_a_grade_employees
2025-06-26 14:22:35.695 | INFO     | src.gui.prototype.prototype_main_window:_on_refresh_data:420 | 刷新数据功能被触发，将强制刷新导航面板。
2025-06-26 14:22:35.732 | WARNING  | src.gui.prototype.prototype_main_window:_on_refresh_data:435 | 导航面板不支持强制刷新。
2025-06-26 14:22:41.642 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2026年 > 4月 > A岗职工', '工资表 > 2025年', '工资表 > 2026年 > 4月 > 全部在职人员', '工资表 > 2026年 > 4月', '工资表 > 2026年']
2025-06-26 14:22:41.642 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 4月 > 全部在职人员
2025-06-26 14:22:41.642 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 14:22:41.642 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2757 | 数据量大(1396条)，启用分页模式
2025-06-26 14:22:41.642 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2785 | 使用分页模式加载 salary_data_2026_04_active_employees，第1页，每页50条
2025-06-26 14:22:41.642 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2794 | 缓存命中: salary_data_2026_04_active_employees 第1页
2025-06-26 14:22:41.642 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2666 | 表 salary_data_2026_04_active_employees 无需字段重命名
2025-06-26 14:22:41.642 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2858 | 分页数据加载成功（缓存）: 50条数据，第1页，总计1396条
2025-06-26 14:22:41.642 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 14:22:41.642 | WARNING  | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2700 | 表 salary_data_2026_04_active_employees 的偏好字段在数据中不存在，显示所有字段
2025-06-26 14:22:41.642 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-26 14:22:41.642 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 50
2025-06-26 14:22:41.642 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 14:22:41.642 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 14:22:41.642 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 14:22:41.676 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 34.37ms
2025-06-26 14:22:41.678 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-26 14:22:41.678 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 1396
2025-06-26 14:22:41.683 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 4月 > 全部在职人员
2025-06-26 14:22:44.003 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2026年 > 4月 > A岗职工', '工资表 > 2026年 > 4月 > 全部在职人员', '工资表 > 2025年', '工资表 > 2026年 > 4月 > 退休人员', '工资表 > 2026年 > 4月']
2025-06-26 14:22:44.003 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 4月 > 退休人员
2025-06-26 14:22:44.003 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 14:22:44.003 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2760 | 数据量小(13条)，使用普通模式
2025-06-26 14:22:44.003 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 4月 > 退休人员
2025-06-26 14:22:44.003 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2919 | 统一加载所有字段（修复字段不一致问题）
2025-06-26 14:22:44.003 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2026_04_pension_employees 获取数据...
2025-06-26 14:22:44.003 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2026_04_pension_employees 获取 13 行数据。
2025-06-26 14:22:44.003 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2926 | 成功从数据库加载 13 行数据，16 个字段
2025-06-26 14:22:44.003 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2662 | 应用字段映射到表 salary_data_2026_04_pension_employees: 7 个字段重命名
2025-06-26 14:22:44.003 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2931 | 应用字段映射后的表头: ['序号', '人员代码', '姓名', 'id_card', '部门', '人员类别代码', 'basic_salary', 'performance_bonus', 'overtime_pay', '住房补贴', 'deduction', '应发合计', 'month', 'year', 'created_at', 'updated_at']
2025-06-26 14:22:44.019 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 14:22:44.019 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2702 | 表 salary_data_2026_04_pension_employees 无字段偏好设置，显示所有字段
2025-06-26 14:22:44.072 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 13 行
2025-06-26 14:22:45.438 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 13
2025-06-26 14:22:45.439 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(13)自动调整最大可见行数为: 13
2025-06-26 14:22:45.439 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 14:22:45.440 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 13 行, 16 列
2025-06-26 14:22:45.457 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 13 行, 最大可见行数: 13, 耗时: 1345.92ms
2025-06-26 14:22:45.457 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 13
2025-06-26 14:22:49.254 | INFO     | src.gui.prototype.prototype_main_window:_on_refresh_data:420 | 刷新数据功能被触发，将强制刷新导航面板。
2025-06-26 14:22:49.254 | WARNING  | src.gui.prototype.prototype_main_window:_on_refresh_data:435 | 导航面板不支持强制刷新。
2025-06-26 14:22:55.956 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2026年 > 4月 > A岗职工', '工资表 > 2026年 > 4月 > 退休人员', '工资表 > 2026年 > 4月 > 全部在职人员', '工资表 > 2025年', '工资表 > 2026年 > 4月 > 离休人员']
2025-06-26 14:22:55.959 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 4月 > 离休人员
2025-06-26 14:22:55.959 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 14:22:55.960 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2760 | 数据量小(2条)，使用普通模式
2025-06-26 14:22:55.960 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 4月 > 离休人员
2025-06-26 14:22:55.961 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2919 | 统一加载所有字段（修复字段不一致问题）
2025-06-26 14:22:55.961 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2026_04_retired_employees 获取数据...
2025-06-26 14:22:55.963 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2026_04_retired_employees 获取 2 行数据。
2025-06-26 14:22:55.963 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2926 | 成功从数据库加载 2 行数据，16 个字段
2025-06-26 14:22:55.963 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2662 | 应用字段映射到表 salary_data_2026_04_retired_employees: 6 个字段重命名
2025-06-26 14:22:55.963 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2931 | 应用字段映射后的表头: ['序号', '人员代码', '姓名', 'id_card', '部门', 'position', 'basic_salary', 'performance_bonus', 'overtime_pay', '离休\n补贴', 'deduction', '合计', 'month', 'year', 'created_at', 'updated_at']
2025-06-26 14:22:55.968 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 14:22:55.970 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2702 | 表 salary_data_2026_04_retired_employees 无字段偏好设置，显示所有字段
2025-06-26 14:22:55.983 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 2 行
2025-06-26 14:22:55.989 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2583 | 最大可见行数过小可能影响性能，建议至少设置为10，当前值: 2
2025-06-26 14:22:56.087 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 13 -> 2
2025-06-26 14:22:56.209 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(2)自动调整最大可见行数为: 2
2025-06-26 14:22:56.210 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 14:22:56.212 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 2 行, 16 列
2025-06-26 14:22:56.222 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 2 行, 最大可见行数: 2, 耗时: 233.37ms
2025-06-26 14:22:56.230 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 2
2025-06-26 14:23:08.210 | INFO     | src.gui.prototype.prototype_main_window:_on_refresh_data:420 | 刷新数据功能被触发，将强制刷新导航面板。
2025-06-26 14:23:08.210 | WARNING  | src.gui.prototype.prototype_main_window:_on_refresh_data:435 | 导航面板不支持强制刷新。
2025-06-26 14:23:18.508 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 14:23:18.508 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设置: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 14:23:18.508 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成功
2025-06-26 14:23:18.508 | INFO     | src.modules.system_config.user_preferences:save_preferences:251 | 正在保存偏好设置: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 14:23:18.508 | INFO     | src.modules.system_config.user_preferences:save_preferences:255 | 偏好设置保存成功
2025-06-26 14:23:18.551 | INFO     | src.gui.prototype.prototype_main_window:_on_menu_visibility_changed:2226 | 菜单栏可见性变化: False
2025-06-26 14:23:18.562 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3254 | 已注册 2 个表格到表头管理器
2025-06-26 14:23:18.622 | INFO     | src.gui.prototype.prototype_main_window:refresh_layout:2286 | 窗口布局全面刷新完成
2025-06-26 14:23:18.624 | INFO     | src.gui.prototype.prototype_main_window:hide_menu_bar:1819 | 菜单栏已隐藏
2025-06-26 14:23:18.624 | INFO     | src.gui.prototype.prototype_main_window:_on_settings_clicked:2210 | 设置按钮点击，切换菜单栏显示状态
2025-06-26 14:23:18.784 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: xl (宽度: 1920px)
2025-06-26 14:23:18.805 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:1241 | MainWorkspaceArea 响应式适配: xl
2025-06-26 14:23:19.124 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2684870815776 已经注册，将覆盖现有注册
2025-06-26 14:23:19.124 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2684870808576 已经注册，将覆盖现有注册
2025-06-26 14:23:19.124 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3254 | 已注册 2 个表格到表头管理器
2025-06-26 14:23:21.241 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 14:23:21.241 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设置: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 14:23:21.241 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成功
2025-06-26 14:23:21.241 | INFO     | src.modules.system_config.user_preferences:save_preferences:251 | 正在保存偏好设置: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 14:23:21.241 | INFO     | src.modules.system_config.user_preferences:save_preferences:255 | 偏好设置保存成功
2025-06-26 14:23:21.347 | INFO     | src.gui.prototype.prototype_main_window:_on_menu_visibility_changed:2226 | 菜单栏可见性变化: True
2025-06-26 14:23:21.349 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2684870815776 已经注册，将覆盖现有注册
2025-06-26 14:23:21.354 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2684870808576 已经注册，将覆盖现有注册
2025-06-26 14:23:21.361 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3254 | 已注册 2 个表格到表头管理器
2025-06-26 14:23:21.458 | INFO     | src.gui.prototype.prototype_main_window:refresh_layout:2286 | 窗口布局全面刷新完成
2025-06-26 14:23:21.459 | INFO     | src.gui.prototype.prototype_main_window:show_menu_bar:1792 | 菜单栏已显示
2025-06-26 14:23:21.472 | INFO     | src.gui.prototype.prototype_main_window:_on_settings_clicked:2210 | 设置按钮点击，切换菜单栏显示状态
2025-06-26 14:23:21.961 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2684870815776 已经注册，将覆盖现有注册
2025-06-26 14:23:21.961 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2684870808576 已经注册，将覆盖现有注册
2025-06-26 14:23:21.961 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3254 | 已注册 2 个表格到表头管理器
2025-06-26 14:23:23.110 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 14:23:23.111 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设置: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 14:23:23.112 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成功
2025-06-26 14:23:23.112 | INFO     | src.modules.system_config.user_preferences:save_preferences:251 | 正在保存偏好设置: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 14:23:23.114 | INFO     | src.modules.system_config.user_preferences:save_preferences:255 | 偏好设置保存成功
2025-06-26 14:23:23.143 | INFO     | src.gui.prototype.prototype_main_window:_on_menu_visibility_changed:2226 | 菜单栏可见性变化: False
2025-06-26 14:23:23.145 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2684870815776 已经注册，将覆盖现有注册
2025-06-26 14:23:23.147 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2684870808576 已经注册，将覆盖现有注册
2025-06-26 14:23:23.148 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3254 | 已注册 2 个表格到表头管理器
2025-06-26 14:23:23.211 | INFO     | src.gui.prototype.prototype_main_window:refresh_layout:2286 | 窗口布局全面刷新完成
2025-06-26 14:23:23.232 | INFO     | src.gui.prototype.prototype_main_window:hide_menu_bar:1819 | 菜单栏已隐藏
2025-06-26 14:23:23.233 | INFO     | src.gui.prototype.prototype_main_window:_on_settings_clicked:2210 | 设置按钮点击，切换菜单栏显示状态
2025-06-26 14:23:23.732 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2684870815776 已经注册，将覆盖现有注册
2025-06-26 14:23:23.732 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2684870808576 已经注册，将覆盖现有注册
2025-06-26 14:23:23.732 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3254 | 已注册 2 个表格到表头管理器
2025-06-26 14:23:27.384 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 14:23:27.384 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设置: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 14:23:27.400 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成功
2025-06-26 14:23:27.400 | INFO     | src.modules.system_config.user_preferences:save_preferences:251 | 正在保存偏好设置: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 14:23:27.400 | INFO     | src.modules.system_config.user_preferences:save_preferences:255 | 偏好设置保存成功
2025-06-26 14:23:27.490 | INFO     | src.gui.prototype.prototype_main_window:_on_menu_visibility_changed:2226 | 菜单栏可见性变化: True
2025-06-26 14:23:27.492 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2684870815776 已经注册，将覆盖现有注册
2025-06-26 14:23:27.492 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2684870808576 已经注册，将覆盖现有注册
2025-06-26 14:23:27.493 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3254 | 已注册 2 个表格到表头管理器
2025-06-26 14:23:27.607 | INFO     | src.gui.prototype.prototype_main_window:refresh_layout:2286 | 窗口布局全面刷新完成
2025-06-26 14:23:27.608 | INFO     | src.gui.prototype.prototype_main_window:show_menu_bar:1792 | 菜单栏已显示
2025-06-26 14:23:27.619 | INFO     | src.gui.prototype.prototype_main_window:_on_settings_clicked:2210 | 设置按钮点击，切换菜单栏显示状态
2025-06-26 14:23:28.108 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2684870815776 已经注册，将覆盖现有注册
2025-06-26 14:23:28.108 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2684870808576 已经注册，将覆盖现有注册
2025-06-26 14:23:28.108 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3254 | 已注册 2 个表格到表头管理器
2025-06-26 14:23:29.270 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 14:23:29.270 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设置: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 14:23:29.271 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成功
2025-06-26 14:23:29.272 | INFO     | src.modules.system_config.user_preferences:save_preferences:251 | 正在保存偏好设置: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 14:23:29.273 | INFO     | src.modules.system_config.user_preferences:save_preferences:255 | 偏好设置保存成功
2025-06-26 14:23:29.301 | INFO     | src.gui.prototype.prototype_main_window:_on_menu_visibility_changed:2226 | 菜单栏可见性变化: False
2025-06-26 14:23:29.302 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2684870815776 已经注册，将覆盖现有注册
2025-06-26 14:23:29.306 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2684870808576 已经注册，将覆盖现有注册
2025-06-26 14:23:29.306 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3254 | 已注册 2 个表格到表头管理器
2025-06-26 14:23:29.362 | INFO     | src.gui.prototype.prototype_main_window:refresh_layout:2286 | 窗口布局全面刷新完成
2025-06-26 14:23:29.388 | INFO     | src.gui.prototype.prototype_main_window:hide_menu_bar:1819 | 菜单栏已隐藏
2025-06-26 14:23:29.388 | INFO     | src.gui.prototype.prototype_main_window:_on_settings_clicked:2210 | 设置按钮点击，切换菜单栏显示状态
2025-06-26 14:23:29.888 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2684870815776 已经注册，将覆盖现有注册
2025-06-26 14:23:29.888 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2684870808576 已经注册，将覆盖现有注册
2025-06-26 14:23:29.888 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3254 | 已注册 2 个表格到表头管理器
2025-06-26 14:23:30.887 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 14:23:30.888 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设置: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 14:23:30.891 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成功
2025-06-26 14:23:30.891 | INFO     | src.modules.system_config.user_preferences:save_preferences:251 | 正在保存偏好设置: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 14:23:30.893 | INFO     | src.modules.system_config.user_preferences:save_preferences:255 | 偏好设置保存成功
2025-06-26 14:23:31.004 | INFO     | src.gui.prototype.prototype_main_window:_on_menu_visibility_changed:2226 | 菜单栏可见性变化: True
2025-06-26 14:23:31.006 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2684870815776 已经注册，将覆盖现有注册
2025-06-26 14:23:31.007 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2684870808576 已经注册，将覆盖现有注册
2025-06-26 14:23:31.007 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3254 | 已注册 2 个表格到表头管理器
2025-06-26 14:23:31.093 | INFO     | src.gui.prototype.prototype_main_window:refresh_layout:2286 | 窗口布局全面刷新完成
2025-06-26 14:23:31.095 | INFO     | src.gui.prototype.prototype_main_window:show_menu_bar:1792 | 菜单栏已显示
2025-06-26 14:23:31.112 | INFO     | src.gui.prototype.prototype_main_window:_on_settings_clicked:2210 | 设置按钮点击，切换菜单栏显示状态
2025-06-26 14:23:31.595 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2684870815776 已经注册，将覆盖现有注册
2025-06-26 14:23:31.595 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2684870808576 已经注册，将覆盖现有注册
2025-06-26 14:23:31.595 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3254 | 已注册 2 个表格到表头管理器
2025-06-26 14:23:33.103 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 14:23:33.103 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设置: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 14:23:33.103 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成功
2025-06-26 14:23:33.103 | INFO     | src.modules.system_config.user_preferences:save_preferences:251 | 正在保存偏好设置: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 14:23:33.103 | INFO     | src.modules.system_config.user_preferences:save_preferences:255 | 偏好设置保存成功
2025-06-26 14:23:33.147 | INFO     | src.gui.prototype.prototype_main_window:_on_menu_visibility_changed:2226 | 菜单栏可见性变化: False
2025-06-26 14:23:33.154 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2684870815776 已经注册，将覆盖现有注册
2025-06-26 14:23:33.158 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2684870808576 已经注册，将覆盖现有注册
2025-06-26 14:23:33.160 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3254 | 已注册 2 个表格到表头管理器
2025-06-26 14:23:33.224 | INFO     | src.gui.prototype.prototype_main_window:refresh_layout:2286 | 窗口布局全面刷新完成
2025-06-26 14:23:33.286 | INFO     | src.gui.prototype.prototype_main_window:hide_menu_bar:1819 | 菜单栏已隐藏
2025-06-26 14:23:33.287 | INFO     | src.gui.prototype.prototype_main_window:_on_settings_clicked:2210 | 设置按钮点击，切换菜单栏显示状态
2025-06-26 14:23:33.785 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2684870815776 已经注册，将覆盖现有注册
2025-06-26 14:23:33.785 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2684870808576 已经注册，将覆盖现有注册
2025-06-26 14:23:33.785 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3254 | 已注册 2 个表格到表头管理器
2025-06-26 14:24:00.124 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data:396 | 数据导入功能被触发，发出 import_requested 信号。
2025-06-26 14:24:00.124 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:2430 | 接收到数据导入请求，推断的目标路径: 工资表 > 2026年 > 4月 > 离休人员。打开导入对话框。
2025-06-26 14:24:00.124 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-06-26 14:24:00.124 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 14:24:00.124 | INFO     | src.modules.data_import.multi_sheet_importer:__init__:64 | 多Sheet导入器初始化完成
2025-06-26 14:24:00.124 | INFO     | src.gui.widgets.target_selection_widget:_load_available_options:250 | 成功加载导航配置
2025-06-26 14:24:00.124 | INFO     | src.gui.widgets.target_selection_widget:set_target_from_path:505 | 从路径设置目标: 工资表 > 2026年 > 4月 > 离休人员
2025-06-26 14:24:00.164 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:702 | 找到 68 个匹配类型 'salary_data' 的表
2025-06-26 14:24:00.189 | INFO     | src.gui.dialogs:_init_field_mapping:1871 | 初始化字段映射表格: 17 个默认字段
2025-06-26 14:24:00.481 | INFO     | src.gui.dialogs:__init__:67 | 数据导入对话框初始化完成。
2025-06-26 14:24:05.631 | INFO     | src.gui.dialogs:_on_target_changed:2026 | 目标位置已更新: 工资表 > 2026年 > 5月 > 离休人员
2025-06-26 14:24:14.114 | INFO     | src.gui.dialogs:_on_target_changed:2026 | 目标位置已更新: 工资表 > 2026年 > 5月 > A岗职工
2025-06-26 14:24:26.677 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-26 14:24:40.191 | INFO     | src.gui.dialogs:_on_import_mode_changed:2064 | 切换到多Sheet导入模式
2025-06-26 14:24:52.349 | INFO     | src.modules.data_import.excel_importer:preview_data:221 | 数据预览完成: 21列, 20行
2025-06-26 14:25:06.927 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-26 14:25:06.927 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:194 | 开始导入多Sheet Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-26 14:25:06.942 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-26 14:25:07.171 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-26 14:25:07.172 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:205 | 检测到 4 个工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-26 14:25:07.173 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-26 14:25:07.173 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-26 14:25:07.173 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-06-26 14:25:07.309 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:315 | 导入完成: 3行 x 12列
2025-06-26 14:25:07.309 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-26 14:25:07.309 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:527 | 过滤记录[行3]: 姓名字段(姓名)为空, 数据: {'序号': '总计', '合计': np.float64(34405.100000000006)}
2025-06-26 14:25:07.309 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:565 | 数据过滤完成: 原始3条记录，过滤1条无效记录，有效记录2条
2025-06-26 14:25:07.309 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:415 | 未找到工作表 离休人员工资表 的配置，使用智能默认处理
2025-06-26 14:25:07.309 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:706 | 为Sheet '离休人员工资表' 生成智能默认配置: 1 个必需字段
2025-06-26 14:25:07.309 | INFO     | src.modules.data_import.auto_field_mapping_generator:generate_mapping:84 | 开始为表 'salary_data_2026_05_retired_employees' 生成字段映射，共 12 个字段
2025-06-26 14:25:07.325 | INFO     | src.modules.data_import.auto_field_mapping_generator:generate_mapping:131 | 字段映射生成完成，详细信息:
2025-06-26 14:25:07.341 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:143 | 字段映射保存成功: salary_data_2026_05_retired_employees
2025-06-26 14:25:07.342 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:467 | 为表 salary_data_2026_05_retired_employees 自动生成字段映射: 8 个字段
2025-06-26 14:25:07.344 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:488 | 映射质量评估: 覆盖率=100.0%, 总体评分=1.00
2025-06-26 14:25:07.344 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:494 | Sheet 离休人员工资表 存在 1 个验证错误
2025-06-26 14:25:07.350 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:503 | Sheet 离休人员工资表 数据处理完成: 2 行
2025-06-26 14:25:07.354 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:840 | 列名映射成功: ['人员代码', '姓名'] -> ['employee_id', 'employee_name']
2025-06-26 14:25:07.355 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:853 | 表 salary_data_2026_05_retired_employees 不存在，将根据模板创建...
2025-06-26 14:25:07.374 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:951 | 成功向表 salary_data_2026_05_retired_employees 保存 2 条数据。
2025-06-26 14:25:07.397 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-26 14:25:07.398 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-26 14:25:07.399 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-06-26 14:25:07.553 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:315 | 导入完成: 14行 x 21列
2025-06-26 14:25:07.557 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-26 14:25:07.557 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:527 | 过滤记录[行14]: 姓名字段(姓名)为空, 数据: {'序号': '总计', '应发工资': np.float64(33607.14625)}
2025-06-26 14:25:07.558 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:565 | 数据过滤完成: 原始14条记录，过滤1条无效记录，有效记录13条
2025-06-26 14:25:07.558 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:415 | 未找到工作表 退休人员工资表 的配置，使用智能默认处理
2025-06-26 14:25:07.559 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:706 | 为Sheet '退休人员工资表' 生成智能默认配置: 1 个必需字段
2025-06-26 14:25:07.559 | INFO     | src.modules.data_import.auto_field_mapping_generator:generate_mapping:84 | 开始为表 'salary_data_2026_05_pension_employees' 生成字段映射，共 21 个字段
2025-06-26 14:25:07.569 | INFO     | src.modules.data_import.auto_field_mapping_generator:generate_mapping:131 | 字段映射生成完成，详细信息:
2025-06-26 14:25:07.577 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:143 | 字段映射保存成功: salary_data_2026_05_pension_employees
2025-06-26 14:25:07.580 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:467 | 为表 salary_data_2026_05_pension_employees 自动生成字段映射: 17 个字段
2025-06-26 14:25:07.581 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:488 | 映射质量评估: 覆盖率=100.0%, 总体评分=1.00
2025-06-26 14:25:07.582 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:494 | Sheet 退休人员工资表 存在 1 个验证错误
2025-06-26 14:25:07.586 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:503 | Sheet 退休人员工资表 数据处理完成: 13 行
2025-06-26 14:25:07.588 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:840 | 列名映射成功: ['人员代码', '姓名', '津贴', '应发工资'] -> ['employee_id', 'employee_name', 'allowance', 'total_salary']
2025-06-26 14:25:07.589 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:853 | 表 salary_data_2026_05_pension_employees 不存在，将根据模板创建...
2025-06-26 14:25:07.610 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:951 | 成功向表 salary_data_2026_05_pension_employees 保存 13 条数据。
2025-06-26 14:25:07.611 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-26 14:25:07.619 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-26 14:25:07.625 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-06-26 14:25:07.779 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:315 | 导入完成: 1397行 x 23列
2025-06-26 14:25:07.779 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-26 14:25:07.779 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:527 | 过滤记录[行1397]: 姓名字段(姓名)为空, 数据: {'2025年岗位工资': np.float64(3971045.71), '2025年薪级工资': np.int64(3138129), '津贴': np.float64(94436.73000000001), '结余津贴': np.int64(78008), '2025年基础性绩效': np.int64(4706933), '卫生费': np.float64(14240.0), '交通补贴': np.float64(306460.0), '物业补贴': np.float64(305860.0), '住房补贴': np.float64(337019.8710385144), '车补': np.float64(12870.0), '通讯补贴': np.float64(10250.0), '2025年奖励性绩效预发': np.int64(2262100), '补发': np.float64(2528.0), '借支': np.float64(122740.12055172413), '应发工资': np.float64(15117140.190486807), '2025公积金': np.int64(2390358), '代扣代存养老保险': np.float64(1967911.5299999986)}
2025-06-26 14:25:07.779 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:565 | 数据过滤完成: 原始1397条记录，过滤1条无效记录，有效记录1396条
2025-06-26 14:25:07.779 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:415 | 未找到工作表 全部在职人员工资表 的配置，使用智能默认处理
2025-06-26 14:25:07.779 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:706 | 为Sheet '全部在职人员工资表' 生成智能默认配置: 2 个必需字段
2025-06-26 14:25:07.779 | INFO     | src.modules.data_import.auto_field_mapping_generator:generate_mapping:84 | 开始为表 'salary_data_2026_05_active_employees' 生成字段映射，共 23 个字段
2025-06-26 14:25:07.795 | INFO     | src.modules.data_import.auto_field_mapping_generator:generate_mapping:131 | 字段映射生成完成，详细信息:
2025-06-26 14:25:07.810 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:143 | 字段映射保存成功: salary_data_2026_05_active_employees
2025-06-26 14:25:07.810 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:467 | 为表 salary_data_2026_05_active_employees 自动生成字段映射: 16 个字段
2025-06-26 14:25:07.810 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:488 | 映射质量评估: 覆盖率=100.0%, 总体评分=1.00
2025-06-26 14:25:07.810 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:494 | Sheet 全部在职人员工资表 存在 2 个验证错误
2025-06-26 14:25:07.810 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:503 | Sheet 全部在职人员工资表 数据处理完成: 1396 行
2025-06-26 14:25:07.810 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:840 | 列名映射成功: ['工号', '姓名', '津贴', '应发工资'] -> ['employee_id', 'employee_name', 'allowance', 'total_salary']
2025-06-26 14:25:07.810 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:853 | 表 salary_data_2026_05_active_employees 不存在，将根据模板创建...
2025-06-26 14:25:07.860 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:951 | 成功向表 salary_data_2026_05_active_employees 保存 1396 条数据。
2025-06-26 14:25:07.880 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-26 14:25:07.885 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-26 14:25:07.885 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-06-26 14:25:08.030 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:315 | 导入完成: 63行 x 17列
2025-06-26 14:25:08.108 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-26 14:25:08.110 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:527 | 过滤记录[行63]: 姓名字段(姓名)为空, 数据: {'应发工资': np.float64(471980.9)}
2025-06-26 14:25:08.113 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:565 | 数据过滤完成: 原始63条记录，过滤1条无效记录，有效记录62条
2025-06-26 14:25:08.161 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:415 | 未找到工作表 A岗职工 的配置，使用智能默认处理
2025-06-26 14:25:08.161 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:706 | 为Sheet 'A岗职工' 生成智能默认配置: 2 个必需字段
2025-06-26 14:25:08.169 | INFO     | src.modules.data_import.auto_field_mapping_generator:generate_mapping:84 | 开始为表 'salary_data_2026_05_a_grade_employees' 生成字段映射，共 17 个字段
2025-06-26 14:25:08.178 | INFO     | src.modules.data_import.auto_field_mapping_generator:generate_mapping:131 | 字段映射生成完成，详细信息:
2025-06-26 14:25:08.186 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:143 | 字段映射保存成功: salary_data_2026_05_a_grade_employees
2025-06-26 14:25:08.186 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:467 | 为表 salary_data_2026_05_a_grade_employees 自动生成字段映射: 13 个字段
2025-06-26 14:25:08.186 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:488 | 映射质量评估: 覆盖率=100.0%, 总体评分=1.00
2025-06-26 14:25:08.189 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:494 | Sheet A岗职工 存在 2 个验证错误
2025-06-26 14:25:08.194 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:503 | Sheet A岗职工 数据处理完成: 62 行
2025-06-26 14:25:08.196 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:840 | 列名映射成功: ['工号', '姓名', '津贴', '应发工资'] -> ['employee_id', 'employee_name', 'allowance', 'total_salary']
2025-06-26 14:25:08.204 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:853 | 表 salary_data_2026_05_a_grade_employees 不存在，将根据模板创建...
2025-06-26 14:25:08.220 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:951 | 成功向表 salary_data_2026_05_a_grade_employees 保存 62 条数据。
2025-06-26 14:25:08.225 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:224 | 多Sheet导入完成: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2026_05_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2026_05_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2026_05_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2026_05_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2026_05_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2026_05_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2026_05_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2026_05_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 6}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']}
2025-06-26 14:25:08.238 | INFO     | src.gui.dialogs:_execute_multi_sheet_import:1425 | 多Sheet导入成功: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2026_05_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2026_05_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2026_05_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2026_05_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2026_05_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2026_05_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2026_05_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2026_05_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 6}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工'], 'import_mode': 'multi_sheet', 'strategy': 'separate_tables', 'data_period': '2026-05', 'data_description': '2026年5月工资数据', 'file_path': 'C:/test/salary_changes/salary_changes/data/工资表/2025年5月份正式工工资（报财务)  最终版.xls', 'target_path': '工资表 > 2026年 > 5月 > A岗职工'}
2025-06-26 14:25:08.241 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:2443 | 开始处理导入结果: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2026_05_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2026_05_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2026_05_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2026_05_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2026_05_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2026_05_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2026_05_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2026_05_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 6}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工'], 'import_mode': 'multi_sheet', 'strategy': 'separate_tables', 'data_period': '2026-05', 'data_description': '2026年5月工资数据', 'file_path': 'C:/test/salary_changes/salary_changes/data/工资表/2025年5月份正式工工资（报财务)  最终版.xls', 'target_path': '工资表 > 2026年 > 5月 > A岗职工'}
2025-06-26 14:25:08.241 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:2454 | 导入模式: multi_sheet, 目标路径: '工资表 > 2026年 > 5月 > A岗职工'
2025-06-26 14:25:08.241 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:2462 | 接收到导入数据, 来源: 未知来源, 目标路径: 工资表 > 2026年 > 5月 > A岗职工
2025-06-26 14:25:08.260 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:2520 | 检查是否需要更新导航面板: ['工资表', '2026年', '5月', 'A岗职工']
2025-06-26 14:25:08.261 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:2524 | 检测到工资数据导入，开始刷新导航面板
2025-06-26 14:25:08.261 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:2528 | 使用强制刷新方法
2025-06-26 14:25:08.262 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1036 | 开始强制刷新工资数据导航... (尝试 1/3)
2025-06-26 14:25:08.262 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1044 | 找到工资表节点: 📊 工资表
2025-06-26 14:25:08.278 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2026年 > 4月 > A岗职工', '工资表 > 2026年 > 4月 > 离休人员', '工资表 > 2026年 > 4月 > 退休人员', '工资表 > 2026年 > 4月 > 全部在职人员', '工资表 > 2025年']
2025-06-26 14:25:08.312 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 4月 > 离休人员
2025-06-26 14:25:08.313 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 14:25:08.313 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2760 | 数据量小(2条)，使用普通模式
2025-06-26 14:25:08.313 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 4月 > 离休人员
2025-06-26 14:25:08.323 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2919 | 统一加载所有字段（修复字段不一致问题）
2025-06-26 14:25:08.326 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2026_04_retired_employees 获取数据...
2025-06-26 14:25:08.328 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2026_04_retired_employees 获取 2 行数据。
2025-06-26 14:25:08.329 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2926 | 成功从数据库加载 2 行数据，16 个字段
2025-06-26 14:25:08.330 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2662 | 应用字段映射到表 salary_data_2026_04_retired_employees: 6 个字段重命名
2025-06-26 14:25:08.330 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2931 | 应用字段映射后的表头: ['序号', '人员代码', '姓名', 'id_card', '部门', 'position', 'basic_salary', 'performance_bonus', 'overtime_pay', '离休\n补贴', 'deduction', '合计', 'month', 'year', 'created_at', 'updated_at']
2025-06-26 14:25:08.332 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 14:25:08.334 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2702 | 表 salary_data_2026_04_retired_employees 无字段偏好设置，显示所有字段
2025-06-26 14:25:08.366 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:702 | 找到 72 个匹配类型 'salary_data' 的表
2025-06-26 14:25:08.381 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2026年，包含 6 个月份
2025-06-26 14:25:08.394 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2025年，包含 3 个月份
2025-06-26 14:25:08.395 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2024年，包含 1 个月份
2025-06-26 14:25:08.395 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2023年，包含 1 个月份
2025-06-26 14:25:08.397 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2022年，包含 1 个月份
2025-06-26 14:25:08.398 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2021年，包含 1 个月份
2025-06-26 14:25:08.400 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2020年，包含 1 个月份
2025-06-26 14:25:08.405 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2019年，包含 1 个月份
2025-06-26 14:25:08.406 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2018年，包含 1 个月份
2025-06-26 14:25:08.407 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2017年，包含 1 个月份
2025-06-26 14:25:08.408 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2016年，包含 1 个月份
2025-06-26 14:25:08.408 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1149 | 工资数据导航已强制刷新: 11 个年份, 18 个月份
2025-06-26 14:25:08.408 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1152 | 默认路径: 工资表 > 2026年 > 1月 > 全部在职人员
2025-06-26 14:25:08.409 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1191 | force_refresh_salary_data 执行完成
2025-06-26 14:25:08.411 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:2533 | 将在800ms后导航到: 工资表 > 2026年 > 5月 > A岗职工
2025-06-26 14:25:08.465 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 2 行
2025-06-26 14:25:08.470 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2583 | 最大可见行数过小可能影响性能，建议至少设置为10，当前值: 2
2025-06-26 14:25:08.470 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 2 -> 2
2025-06-26 14:25:08.471 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(2)自动调整最大可见行数为: 2
2025-06-26 14:25:08.471 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 14:25:08.472 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 2 行, 16 列
2025-06-26 14:25:08.507 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 2 行, 最大可见行数: 2, 耗时: 36.79ms
2025-06-26 14:25:08.531 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 2
2025-06-26 14:25:09.212 | INFO     | src.gui.prototype.prototype_main_window:_navigate_to_imported_path:2566 | 尝试导航到新导入的路径: 工资表 > 2026年 > 5月 > A岗职工
2025-06-26 14:25:09.225 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2026年 > 4月 > A岗职工', '工资表 > 2026年 > 4月 > 离休人员', '工资表 > 2026年 > 4月 > 退休人员', '工资表 > 2026年 > 4月 > 全部在职人员', '工资表 > 2025年']
2025-06-26 14:25:09.229 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 5月 > A岗职工
2025-06-26 14:25:09.238 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 14:25:09.238 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2757 | 数据量大(62条)，启用分页模式
2025-06-26 14:25:09.239 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2785 | 使用分页模式加载 salary_data_2026_05_a_grade_employees，第1页，每页50条
2025-06-26 14:25:09.241 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2828 | 缓存未命中，从数据库加载: salary_data_2026_05_a_grade_employees 第1页
2025-06-26 14:25:09.241 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 5月 > A岗职工
2025-06-26 14:25:09.242 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2026_05_a_grade_employees 第1页数据，每页50条
2025-06-26 14:25:09.243 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_05_a_grade_employees 分页获取数据: 第1页, 每页50条
2025-06-26 14:25:09.247 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_05_a_grade_employees 获取第1页数据: 50 行，总计62行
2025-06-26 14:25:09.248 | INFO     | src.gui.prototype.prototype_main_window:_navigate_to_imported_path:2571 | 已成功导航到新导入的路径: 工资表 > 2026年 > 5月 > A岗职工
2025-06-26 14:25:09.248 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 14:25:09.250 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2662 | 应用字段映射到表 salary_data_2026_05_a_grade_employees: 7 个字段重命名
2025-06-26 14:25:09.252 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 62
2025-06-26 14:25:09.322 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2858 | 分页数据加载成功（数据库）: 50条数据，第1页，总计62条
2025-06-26 14:25:09.331 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_05_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-26 14:25:09.332 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 14:25:09.335 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2702 | 表 salary_data_2026_05_a_grade_employees 无字段偏好设置，显示所有字段
2025-06-26 14:25:09.336 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-26 14:25:09.339 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 2 -> 50
2025-06-26 14:25:09.342 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_05_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-26 14:25:09.342 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 14:25:09.361 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 14:25:09.362 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 14:25:09.382 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 43.02ms
2025-06-26 14:25:09.385 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-26 14:25:09.385 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 62
2025-06-26 14:25:25.978 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-26 14:25:25.979 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 14:25:25.979 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 14:25:25.980 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_05_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-26 14:25:25.982 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_05_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-26 14:25:27.282 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 12
2025-06-26 14:25:27.282 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(12)自动调整最大可见行数为: 12
2025-06-26 14:25:27.283 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 14:25:27.283 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 12 行, 16 列
2025-06-26 14:25:27.286 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 12 行, 最大可见行数: 12, 耗时: 1302.72ms
2025-06-26 14:25:27.287 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第2页, 显示12条记录，字段数: 16
2025-06-26 14:25:27.287 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 2
2025-06-26 14:25:33.070 | INFO     | src.gui.prototype.prototype_main_window:_on_refresh_data:420 | 刷新数据功能被触发，将强制刷新导航面板。
2025-06-26 14:25:33.070 | WARNING  | src.gui.prototype.prototype_main_window:_on_refresh_data:435 | 导航面板不支持强制刷新。
2025-06-26 14:25:52.686 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-26 14:25:52.686 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 14:25:52.686 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 14:25:52.686 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_05_a_grade_employees 分页获取数据: 第1页, 每页50条
2025-06-26 14:25:52.686 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_05_a_grade_employees 获取第1页数据: 50 行，总计62行
2025-06-26 14:25:52.686 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 12 -> 50
2025-06-26 14:25:52.686 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 14:25:52.686 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 14:25:52.686 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 14:25:52.701 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 15.16ms
2025-06-26 14:25:52.701 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第1页, 显示50条记录，字段数: 16
2025-06-26 14:25:52.701 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 1
2025-06-26 14:26:05.697 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2026年 > 4月 > A岗职工', '工资表 > 2026年 > 4月 > 离休人员', '工资表 > 2026年 > 4月 > 退休人员', '工资表 > 2026年 > 4月 > 全部在职人员', '工资表 > 2025年']
2025-06-26 14:26:05.697 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 5月 > 退休人员
2025-06-26 14:26:05.697 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 14:26:05.697 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2760 | 数据量小(13条)，使用普通模式
2025-06-26 14:26:05.697 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 5月 > 退休人员
2025-06-26 14:26:05.713 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2919 | 统一加载所有字段（修复字段不一致问题）
2025-06-26 14:26:05.713 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2026_05_pension_employees 获取数据...
2025-06-26 14:26:05.713 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2026_05_pension_employees 获取 13 行数据。
2025-06-26 14:26:05.713 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2926 | 成功从数据库加载 13 行数据，16 个字段
2025-06-26 14:26:05.713 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2662 | 应用字段映射到表 salary_data_2026_05_pension_employees: 7 个字段重命名
2025-06-26 14:26:05.713 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2931 | 应用字段映射后的表头: ['序号', '人员代码', '姓名', 'id_card', '部门', '人员类别代码', 'basic_salary', 'performance_bonus', 'overtime_pay', '住房补贴', 'deduction', '应发合计', 'month', 'year', 'created_at', 'updated_at']
2025-06-26 14:26:05.713 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 14:26:05.713 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2702 | 表 salary_data_2026_05_pension_employees 无字段偏好设置，显示所有字段
2025-06-26 14:26:05.789 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 13 行
2025-06-26 14:26:06.817 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 13
2025-06-26 14:26:06.818 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(13)自动调整最大可见行数为: 13
2025-06-26 14:26:06.819 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 14:26:06.820 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 13 行, 16 列
2025-06-26 14:26:06.828 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 13 行, 最大可见行数: 13, 耗时: 1039.27ms
2025-06-26 14:26:06.829 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 13
2025-06-26 14:26:12.610 | INFO     | src.gui.prototype.prototype_main_window:_on_refresh_data:420 | 刷新数据功能被触发，将强制刷新导航面板。
2025-06-26 14:26:12.610 | WARNING  | src.gui.prototype.prototype_main_window:_on_refresh_data:435 | 导航面板不支持强制刷新。
2025-06-26 14:26:16.069 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2026年 > 4月 > A岗职工', '工资表 > 2026年 > 4月 > 离休人员', '工资表 > 2026年 > 4月 > 退休人员', '工资表 > 2026年 > 4月 > 全部在职人员', '工资表 > 2025年']
2025-06-26 14:26:16.070 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 5月 > 离休人员
2025-06-26 14:26:16.071 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 14:26:16.071 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2760 | 数据量小(2条)，使用普通模式
2025-06-26 14:26:16.072 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 5月 > 离休人员
2025-06-26 14:26:16.072 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2919 | 统一加载所有字段（修复字段不一致问题）
2025-06-26 14:26:16.073 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2026_05_retired_employees 获取数据...
2025-06-26 14:26:16.074 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2026_05_retired_employees 获取 2 行数据。
2025-06-26 14:26:16.075 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2926 | 成功从数据库加载 2 行数据，16 个字段
2025-06-26 14:26:16.077 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2662 | 应用字段映射到表 salary_data_2026_05_retired_employees: 5 个字段重命名
2025-06-26 14:26:16.078 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2931 | 应用字段映射后的表头: ['序号', '人员代码', '姓名', 'id_card', '部门', 'position', 'basic_salary', 'performance_bonus', 'overtime_pay', '离休\n补贴', 'deduction', 'total_salary', 'month', 'year', 'created_at', 'updated_at']
2025-06-26 14:26:16.079 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 14:26:16.080 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2702 | 表 salary_data_2026_05_retired_employees 无字段偏好设置，显示所有字段
2025-06-26 14:26:16.084 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 2 行
2025-06-26 14:26:16.086 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2583 | 最大可见行数过小可能影响性能，建议至少设置为10，当前值: 2
2025-06-26 14:26:16.175 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 13 -> 2
2025-06-26 14:26:16.176 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(2)自动调整最大可见行数为: 2
2025-06-26 14:26:16.177 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 14:26:16.178 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 2 行, 16 列
2025-06-26 14:26:16.183 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 2 行, 最大可见行数: 2, 耗时: 96.92ms
2025-06-26 14:26:16.184 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 2
2025-06-26 14:26:17.855 | INFO     | src.gui.prototype.prototype_main_window:_on_refresh_data:420 | 刷新数据功能被触发，将强制刷新导航面板。
2025-06-26 14:26:17.856 | WARNING  | src.gui.prototype.prototype_main_window:_on_refresh_data:435 | 导航面板不支持强制刷新。
2025-06-26 14:26:23.048 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2026年 > 4月 > A岗职工', '工资表 > 2026年 > 4月 > 离休人员', '工资表 > 2026年 > 4月 > 退休人员', '工资表 > 2026年 > 4月 > 全部在职人员', '工资表 > 2025年']
2025-06-26 14:26:23.048 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 5月 > 全部在职人员
2025-06-26 14:26:23.048 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 14:26:23.048 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2757 | 数据量大(1396条)，启用分页模式
2025-06-26 14:26:23.048 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2785 | 使用分页模式加载 salary_data_2026_05_active_employees，第1页，每页50条
2025-06-26 14:26:23.048 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2828 | 缓存未命中，从数据库加载: salary_data_2026_05_active_employees 第1页
2025-06-26 14:26:23.048 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 5月 > 全部在职人员
2025-06-26 14:26:23.048 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2026_05_active_employees 第1页数据，每页50条
2025-06-26 14:26:23.048 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_05_active_employees 分页获取数据: 第1页, 每页50条
2025-06-26 14:26:23.048 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_05_active_employees 获取第1页数据: 50 行，总计1396行
2025-06-26 14:26:23.048 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 14:26:23.048 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2662 | 应用字段映射到表 salary_data_2026_05_active_employees: 7 个字段重命名
2025-06-26 14:26:23.064 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 1396
2025-06-26 14:26:23.097 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2858 | 分页数据加载成功（数据库）: 50条数据，第1页，总计1396条
2025-06-26 14:26:23.118 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_05_active_employees 分页获取数据: 第2页, 每页50条
2025-06-26 14:26:23.118 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 14:26:23.137 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_05_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-26 14:26:23.150 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2702 | 表 salary_data_2026_05_active_employees 无字段偏好设置，显示所有字段
2025-06-26 14:26:23.163 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-26 14:26:23.163 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 2 -> 50
2025-06-26 14:26:23.163 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 14:26:23.163 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 14:26:23.163 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 14:26:23.185 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 22.21ms
2025-06-26 14:26:23.185 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-26 14:26:23.185 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 1396
2025-06-26 14:26:26.097 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-26 14:26:26.097 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 14:26:26.097 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 14:26:26.097 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_05_active_employees 分页获取数据: 第2页, 每页50条
2025-06-26 14:26:26.113 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_05_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-26 14:26:26.113 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 50
2025-06-26 14:26:26.113 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 14:26:26.113 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 14:26:26.113 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 14:26:30.453 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 4339.93ms
2025-06-26 14:26:30.453 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第2页, 显示50条记录，字段数: 16
2025-06-26 14:26:30.453 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 2
2025-06-26 14:26:41.663 | INFO     | __main__:main:302 | 应用程序正常退出
2025-06-26 14:46:11.426 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-26 14:46:11.426 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-26 14:46:11.426 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-26 14:46:11.426 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-26 14:46:11.426 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\test\..\logs\salary_system.log
2025-06-26 14:46:11.426 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-26 14:46:12.233 | INFO     | src.modules.data_import.import_defaults_manager:find_matching_sheet:197 | 找到精确匹配: 全部在职人员 -> 全部在职人员
2025-06-26 14:46:12.233 | INFO     | src.modules.data_import.import_defaults_manager:find_matching_sheet:215 | 找到模糊匹配: 离休人员 -> 离休人员表 (得分: 0.80)
2025-06-26 14:46:12.233 | INFO     | src.modules.data_import.import_defaults_manager:find_matching_sheet:218 | 未找到匹配的工作表: 不存在的类别
2025-06-26 14:46:12.233 | INFO     | src.modules.data_import.import_defaults_manager:load_settings:86 | 未找到用户设置文件，使用默认设置
2025-06-26 14:46:12.233 | INFO     | src.modules.data_import.import_defaults_manager:save_settings:107 | 导入设置保存成功
2025-06-26 14:46:12.233 | INFO     | src.modules.data_import.import_defaults_manager:load_settings:83 | 成功加载用户自定义导入设置
2025-06-26 14:46:12.249 | INFO     | src.modules.data_import.import_defaults_manager:load_settings:86 | 未找到用户设置文件，使用默认设置
2025-06-26 14:46:12.249 | INFO     | src.modules.data_import.import_defaults_manager:load_settings:86 | 未找到用户设置文件，使用默认设置
2025-06-26 14:46:12.249 | INFO     | src.modules.data_import.import_defaults_manager:load_settings:86 | 未找到用户设置文件，使用默认设置
2025-06-26 14:46:12.265 | INFO     | src.modules.data_import.smart_sheet_matcher:record_user_choice:305 | 记录用户选择: 全部在职人员 -> 工资表B
2025-06-26 14:46:12.266 | INFO     | src.modules.data_import.smart_sheet_matcher:record_user_choice:305 | 记录用户选择: 全部在职人员 -> 工资表B
2025-06-26 14:46:12.268 | INFO     | src.modules.data_import.import_defaults_manager:load_settings:86 | 未找到用户设置文件，使用默认设置
2025-06-26 14:46:12.268 | INFO     | src.modules.data_import.smart_sheet_matcher:record_user_choice:305 | 记录用户选择: 全部在职人员 -> 在职人员工资表
2025-06-26 14:46:47.786 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-26 14:46:47.786 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-26 14:46:47.786 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-26 14:46:47.786 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-26 14:46:47.786 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\test\..\logs\salary_system.log
2025-06-26 14:46:47.786 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-26 14:46:48.527 | INFO     | src.modules.data_import.import_defaults_manager:load_settings:86 | 未找到用户设置文件，使用默认设置
2025-06-26 14:46:48.543 | INFO     | src.modules.data_import.import_defaults_manager:load_settings:86 | 未找到用户设置文件，使用默认设置
2025-06-26 14:46:48.543 | INFO     | src.modules.data_import.import_defaults_manager:load_settings:86 | 未找到用户设置文件，使用默认设置
2025-06-26 14:46:48.543 | INFO     | src.modules.data_import.import_defaults_manager:load_settings:86 | 未找到用户设置文件，使用默认设置
2025-06-26 14:46:48.543 | INFO     | src.modules.data_import.import_defaults_manager:load_settings:86 | 未找到用户设置文件，使用默认设置
2025-06-26 14:46:48.543 | INFO     | src.modules.data_import.import_defaults_manager:load_settings:86 | 未找到用户设置文件，使用默认设置
2025-06-26 14:46:48.563 | INFO     | src.modules.data_import.smart_sheet_matcher:record_user_choice:305 | 记录用户选择: 全部在职人员 -> 工资表B
2025-06-26 14:46:48.573 | INFO     | src.modules.data_import.smart_sheet_matcher:record_user_choice:305 | 记录用户选择: 全部在职人员 -> 工资表B
2025-06-26 14:46:48.575 | INFO     | src.modules.data_import.smart_sheet_matcher:record_user_choice:305 | 记录用户选择: 全部在职人员 -> 工资表B
2025-06-26 15:13:02.717 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-26 15:13:02.717 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-26 15:13:02.718 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-26 15:13:02.718 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-26 15:13:02.719 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\test\..\logs\salary_system.log
2025-06-26 15:13:02.719 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-26 15:14:20.944 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-26 15:14:20.944 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-26 15:14:20.945 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-26 15:14:20.945 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-26 15:14:20.946 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\test\..\logs\salary_system.log
2025-06-26 15:14:20.947 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-26 15:14:21.873 | INFO     | src.modules.data_import.smart_sheet_matcher:clear_cache:417 | 缓存已清空
2025-06-26 15:20:49.085 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-26 15:20:49.085 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-26 15:20:49.085 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-26 15:20:49.085 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-26 15:20:49.085 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-26 15:20:49.085 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-26 15:20:50.397 | INFO     | __main__:setup_app_logging:206 | 月度工资异动处理系统 v2.0.0-refactored 启动
2025-06-26 15:20:50.397 | INFO     | __main__:main:270 | 初始化核心管理器...
2025-06-26 15:20:50.397 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 15:20:50.397 | INFO     | src.modules.system_config.config_manager:load_config:338 | 正在加载配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 15:20:50.397 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:252 | 配置文件验证通过
2025-06-26 15:20:50.397 | INFO     | src.modules.system_config.config_manager:load_config:350 | 配置文件加载成功
2025-06-26 15:20:50.412 | INFO     | src.modules.data_storage.database_manager:_initialize_database:101 | 开始初始化数据库...
2025-06-26 15:20:50.412 | INFO     | src.modules.data_storage.database_manager:_initialize_database:156 | 数据库初始化完成
2025-06-26 15:20:50.412 | INFO     | src.modules.data_storage.database_manager:__init__:97 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-06-26 15:20:50.412 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 15:20:50.412 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:102 | 动态表管理器初始化完成
2025-06-26 15:20:50.412 | INFO     | __main__:main:275 | 核心管理器初始化完成。
2025-06-26 15:20:50.412 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成 - 最大条目数: 50, 最大内存: 50MB
2025-06-26 15:20:50.412 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-06-26 15:20:50.412 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-06-26 15:20:50.710 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:1493 | 菜单栏创建完成
2025-06-26 15:20:50.710 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 15:20:50.710 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设置: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 15:20:50.710 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成功
2025-06-26 15:20:50.710 | INFO     | src.gui.prototype.prototype_main_window:__init__:1469 | 菜单栏管理器初始化完成
2025-06-26 15:20:50.710 | INFO     | src.gui.table_header_manager:__init__:68 | 表头管理器初始化完成
2025-06-26 15:20:50.710 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:2170 | 管理器设置完成，包含增强版表头管理器
2025-06-26 15:20:50.710 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_state:151 | 导航状态加载成功
2025-06-26 15:20:50.710 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-06-26 15:20:50.725 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:_load_data:556 | 数据加载成功: state/user/tree_expansion_data.json
2025-06-26 15:20:50.725 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:148 | 智能树形展开算法初始化完成
2025-06-26 15:20:50.741 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:916 | 开始从元数据动态加载工资数据...
2025-06-26 15:20:50.741 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:923 | 检测到首次启动，将延迟加载导航数据确保数据库就绪
2025-06-26 15:20:50.752 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:654 | 导航面板已重构：移除功能性导航，专注数据导航
2025-06-26 15:20:50.785 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:689 | 恢复导航状态: 92个展开项
2025-06-26 15:20:50.800 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '异动人员表 > 2025年 > 5月', '工资表', '异动人员表', '异动人员表 > 2025年 > 4月']
2025-06-26 15:20:50.801 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:495 | 增强导航面板初始化完成
2025-06-26 15:20:50.874 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1264 | 快捷键注册完成: 18/18 个
2025-06-26 15:20:50.882 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1475 | 拖拽排序管理器初始化完成
2025-06-26 15:20:50.884 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 15:20:50.884 | INFO     | src.modules.data_import.header_edit_manager:__init__:74 | 表头编辑管理器初始化完成
2025-06-26 15:20:50.886 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1851 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-06-26 15:20:50.889 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 15:20:50.891 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 15:20:50.900 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 10.40ms
2025-06-26 15:20:50.901 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 15:20:50.931 | INFO     | src.gui.widgets.pagination_widget:__init__:88 | 分页组件初始化完成
2025-06-26 15:20:50.967 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:379 | 控制面板按钮信号连接完成
2025-06-26 15:20:51.007 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 15:20:51.008 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 15:20:51.009 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:2143 | 快捷键设置完成
2025-06-26 15:20:51.009 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:2132 | 主窗口UI设置完成。
2025-06-26 15:20:51.009 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:2198 | 信号连接设置完成
2025-06-26 15:20:51.010 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 15:20:51.097 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:2627 | 已加载字段映射信息，共48个表的映射
2025-06-26 15:20:51.098 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 15:20:51.099 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 15:20:51.100 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 15:20:51.101 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 2.06ms
2025-06-26 15:20:51.101 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 15:20:51.102 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 15:20:51.102 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 15:20:51.103 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 15:20:51.104 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 15:20:51.104 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 1.05ms
2025-06-26 15:20:51.105 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 15:20:51.105 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 15:20:51.106 | INFO     | src.gui.prototype.prototype_main_window:__init__:2084 | 原型主窗口初始化完成
2025-06-26 15:20:51.466 | INFO     | __main__:main:297 | 应用程序启动成功
2025-06-26 15:20:51.472 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1266px)
2025-06-26 15:20:51.472 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:1241 | MainWorkspaceArea 响应式适配: sm
2025-06-26 15:20:51.541 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_load_salary_data:938 | 执行延迟的工资数据加载...
2025-06-26 15:20:51.541 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1036 | 开始强制刷新工资数据导航... (尝试 1/3)
2025-06-26 15:20:51.542 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1044 | 找到工资表节点: 📊 工资表
2025-06-26 15:20:51.582 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:702 | 找到 72 个匹配类型 'salary_data' 的表
2025-06-26 15:20:51.583 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2026年，包含 6 个月份
2025-06-26 15:20:51.585 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2025年，包含 3 个月份
2025-06-26 15:20:51.586 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2024年，包含 1 个月份
2025-06-26 15:20:51.586 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2023年，包含 1 个月份
2025-06-26 15:20:51.587 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2022年，包含 1 个月份
2025-06-26 15:20:51.588 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2021年，包含 1 个月份
2025-06-26 15:20:51.588 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2020年，包含 1 个月份
2025-06-26 15:20:51.589 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2019年，包含 1 个月份
2025-06-26 15:20:51.589 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2018年，包含 1 个月份
2025-06-26 15:20:51.590 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2017年，包含 1 个月份
2025-06-26 15:20:51.590 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2016年，包含 1 个月份
2025-06-26 15:20:51.591 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1149 | 工资数据导航已强制刷新: 11 个年份, 18 个月份
2025-06-26 15:20:51.591 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1152 | 默认路径: 工资表 > 2026年 > 1月 > 全部在职人员
2025-06-26 15:20:51.593 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1191 | force_refresh_salary_data 执行完成
2025-06-26 15:21:02.398 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 15:21:02.398 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设置: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 15:21:02.398 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成功
2025-06-26 15:21:02.398 | INFO     | src.modules.system_config.user_preferences:save_preferences:251 | 正在保存偏好设置: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 15:21:02.398 | INFO     | src.modules.system_config.user_preferences:save_preferences:255 | 偏好设置保存成功
2025-06-26 15:21:02.436 | INFO     | src.gui.prototype.prototype_main_window:_on_menu_visibility_changed:2226 | 菜单栏可见性变化: True
2025-06-26 15:21:02.438 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3254 | 已注册 2 个表格到表头管理器
2025-06-26 15:21:02.440 | INFO     | src.gui.prototype.prototype_main_window:refresh_layout:2286 | 窗口布局全面刷新完成
2025-06-26 15:21:02.440 | INFO     | src.gui.prototype.prototype_main_window:show_menu_bar:1792 | 菜单栏已显示
2025-06-26 15:21:02.452 | INFO     | src.gui.prototype.prototype_main_window:_on_settings_clicked:2210 | 设置按钮点击，切换菜单栏显示状态
2025-06-26 15:21:02.941 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2462401394544 已经注册，将覆盖现有注册
2025-06-26 15:21:02.941 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2462401387344 已经注册，将覆盖现有注册
2025-06-26 15:21:02.941 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3254 | 已注册 2 个表格到表头管理器
2025-06-26 15:21:17.568 | INFO     | src.gui.prototype.prototype_main_window:_on_menu_action_triggered:2295 | 菜单动作触发: detection_results
2025-06-26 15:21:22.304 | INFO     | src.gui.prototype.prototype_main_window:_on_menu_action_triggered:2295 | 菜单动作触发: preferences
2025-06-26 15:21:26.917 | INFO     | src.gui.prototype.prototype_main_window:_on_menu_action_triggered:2295 | 菜单动作触发: about
2025-06-26 15:21:35.559 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表', '异动人员表', '异动人员表 > 2025年 > 4月', '异动人员表 > 2025年 > 5月']
2025-06-26 15:21:35.559 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年
2025-06-26 15:21:35.559 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 15:21:35.559 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 15:21:35.559 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 15:21:35.559 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 0.00ms
2025-06-26 15:21:35.559 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 15:21:35.559 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 15:21:35.559 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 15:21:35.559 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 15:21:35.559 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 15:21:35.559 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 0.00ms
2025-06-26 15:21:35.559 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 15:21:35.559 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 15:21:35.575 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年
2025-06-26 15:21:37.453 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2026年', '工资表', '异动人员表', '异动人员表 > 2025年 > 4月']
2025-06-26 15:21:37.453 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 5月
2025-06-26 15:21:37.453 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 15:21:37.453 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 15:21:37.469 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 15:21:37.469 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 15.68ms
2025-06-26 15:21:37.469 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 15:21:37.469 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 15:21:37.469 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 15:21:37.469 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 15:21:37.469 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 15:21:37.469 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 0.00ms
2025-06-26 15:21:37.469 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 15:21:37.469 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 15:21:37.469 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 5月
2025-06-26 15:21:39.505 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2026年 > 5月', '工资表 > 2026年', '工资表', '异动人员表']
2025-06-26 15:21:39.505 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 5月 > A岗职工
2025-06-26 15:21:39.505 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 15:21:39.505 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2757 | 数据量大(62条)，启用分页模式
2025-06-26 15:21:39.505 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2785 | 使用分页模式加载 salary_data_2026_05_a_grade_employees，第1页，每页50条
2025-06-26 15:21:39.505 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2828 | 缓存未命中，从数据库加载: salary_data_2026_05_a_grade_employees 第1页
2025-06-26 15:21:39.505 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 5月 > A岗职工
2025-06-26 15:21:39.505 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2026_05_a_grade_employees 第1页数据，每页50条
2025-06-26 15:21:39.505 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_05_a_grade_employees 分页获取数据: 第1页, 每页50条
2025-06-26 15:21:39.505 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_05_a_grade_employees 获取第1页数据: 50 行，总计62行
2025-06-26 15:21:39.505 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 15:21:39.505 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2662 | 应用字段映射到表 salary_data_2026_05_a_grade_employees: 7 个字段重命名
2025-06-26 15:21:39.505 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 62
2025-06-26 15:21:39.505 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2858 | 分页数据加载成功（数据库）: 50条数据，第1页，总计62条
2025-06-26 15:21:39.521 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_05_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-26 15:21:39.521 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 15:21:39.521 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2702 | 表 salary_data_2026_05_a_grade_employees 无字段偏好设置，显示所有字段
2025-06-26 15:21:39.521 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-26 15:21:39.521 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_05_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-26 15:21:39.521 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 1000 -> 50
2025-06-26 15:21:39.536 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 15:21:39.536 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 15:21:39.536 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 15:21:39.576 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 55.80ms
2025-06-26 15:21:39.586 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-26 15:21:39.587 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 62
2025-06-26 15:21:42.948 | INFO     | src.gui.prototype.prototype_main_window:_on_refresh_data:420 | 刷新数据功能被触发，将强制刷新导航面板。
2025-06-26 15:21:42.948 | WARNING  | src.gui.prototype.prototype_main_window:_on_refresh_data:435 | 导航面板不支持强制刷新。
2025-06-26 15:21:51.079 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-26 15:21:51.079 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 15:21:51.079 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 15:21:51.079 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_05_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-26 15:21:51.079 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_05_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-26 15:21:52.549 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 12
2025-06-26 15:21:52.549 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(12)自动调整最大可见行数为: 12
2025-06-26 15:21:52.549 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 15:21:52.549 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 12 行, 16 列
2025-06-26 15:21:52.565 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 12 行, 最大可见行数: 12, 耗时: 1485.75ms
2025-06-26 15:21:52.565 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第2页, 显示12条记录，字段数: 16
2025-06-26 15:21:52.565 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 2
2025-06-26 15:22:04.660 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2026年 > 5月', '工资表 > 2026年', '工资表', '异动人员表']
2025-06-26 15:22:04.676 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 5月 > 离休人员
2025-06-26 15:22:04.676 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 15:22:04.676 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2760 | 数据量小(2条)，使用普通模式
2025-06-26 15:22:04.676 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 5月 > 离休人员
2025-06-26 15:22:04.676 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2919 | 统一加载所有字段（修复字段不一致问题）
2025-06-26 15:22:04.676 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2026_05_retired_employees 获取数据...
2025-06-26 15:22:04.676 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2026_05_retired_employees 获取 2 行数据。
2025-06-26 15:22:04.676 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2926 | 成功从数据库加载 2 行数据，16 个字段
2025-06-26 15:22:04.676 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2662 | 应用字段映射到表 salary_data_2026_05_retired_employees: 5 个字段重命名
2025-06-26 15:22:04.676 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2931 | 应用字段映射后的表头: ['序号', '人员代码', '姓名', 'id_card', '部门', 'position', 'basic_salary', 'performance_bonus', 'overtime_pay', '离休\n补贴', 'deduction', 'total_salary', 'month', 'year', 'created_at', 'updated_at']
2025-06-26 15:22:04.676 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 15:22:04.691 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2702 | 表 salary_data_2026_05_retired_employees 无字段偏好设置，显示所有字段
2025-06-26 15:22:04.697 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 2 行
2025-06-26 15:22:04.700 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2583 | 最大可见行数过小可能影响性能，建议至少设置为10，当前值: 2
2025-06-26 15:22:04.778 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 12 -> 2
2025-06-26 15:22:04.787 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(2)自动调整最大可见行数为: 2
2025-06-26 15:22:04.787 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 15:22:04.790 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 2 行, 16 列
2025-06-26 15:22:04.800 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 2 行, 最大可见行数: 2, 耗时: 100.80ms
2025-06-26 15:22:04.804 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 2
2025-06-26 15:22:08.320 | INFO     | src.gui.prototype.prototype_main_window:_on_refresh_data:420 | 刷新数据功能被触发，将强制刷新导航面板。
2025-06-26 15:22:08.321 | WARNING  | src.gui.prototype.prototype_main_window:_on_refresh_data:435 | 导航面板不支持强制刷新。
2025-06-26 15:22:12.689 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2026年 > 5月', '工资表 > 2026年', '工资表', '异动人员表']
2025-06-26 15:22:12.689 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 5月 > 退休人员
2025-06-26 15:22:12.689 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 15:22:12.689 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2760 | 数据量小(13条)，使用普通模式
2025-06-26 15:22:12.689 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 5月 > 退休人员
2025-06-26 15:22:12.689 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2919 | 统一加载所有字段（修复字段不一致问题）
2025-06-26 15:22:12.689 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2026_05_pension_employees 获取数据...
2025-06-26 15:22:12.689 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2026_05_pension_employees 获取 13 行数据。
2025-06-26 15:22:12.689 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2926 | 成功从数据库加载 13 行数据，16 个字段
2025-06-26 15:22:12.689 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2662 | 应用字段映射到表 salary_data_2026_05_pension_employees: 7 个字段重命名
2025-06-26 15:22:12.689 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2931 | 应用字段映射后的表头: ['序号', '人员代码', '姓名', 'id_card', '部门', '人员类别代码', 'basic_salary', 'performance_bonus', 'overtime_pay', '住房补贴', 'deduction', '应发合计', 'month', 'year', 'created_at', 'updated_at']
2025-06-26 15:22:12.689 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 15:22:12.689 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2702 | 表 salary_data_2026_05_pension_employees 无字段偏好设置，显示所有字段
2025-06-26 15:22:12.689 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 13 行
2025-06-26 15:22:12.689 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 2 -> 13
2025-06-26 15:22:12.689 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(13)自动调整最大可见行数为: 13
2025-06-26 15:22:12.689 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 15:22:12.689 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 13 行, 16 列
2025-06-26 15:22:12.707 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 13 行, 最大可见行数: 13, 耗时: 18.10ms
2025-06-26 15:22:12.707 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 13
2025-06-26 15:22:14.146 | INFO     | src.gui.prototype.prototype_main_window:_on_refresh_data:420 | 刷新数据功能被触发，将强制刷新导航面板。
2025-06-26 15:22:14.146 | WARNING  | src.gui.prototype.prototype_main_window:_on_refresh_data:435 | 导航面板不支持强制刷新。
2025-06-26 15:22:18.360 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2026年 > 5月', '工资表 > 2026年', '工资表', '异动人员表']
2025-06-26 15:22:18.360 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 5月 > 全部在职人员
2025-06-26 15:22:18.360 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 15:22:18.360 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2757 | 数据量大(1396条)，启用分页模式
2025-06-26 15:22:18.360 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2785 | 使用分页模式加载 salary_data_2026_05_active_employees，第1页，每页50条
2025-06-26 15:22:18.360 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2828 | 缓存未命中，从数据库加载: salary_data_2026_05_active_employees 第1页
2025-06-26 15:22:18.360 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 5月 > 全部在职人员
2025-06-26 15:22:18.360 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2026_05_active_employees 第1页数据，每页50条
2025-06-26 15:22:18.375 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_05_active_employees 分页获取数据: 第1页, 每页50条
2025-06-26 15:22:18.375 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_05_active_employees 获取第1页数据: 50 行，总计1396行
2025-06-26 15:22:18.375 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 15:22:18.375 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2662 | 应用字段映射到表 salary_data_2026_05_active_employees: 7 个字段重命名
2025-06-26 15:22:18.375 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 1396
2025-06-26 15:22:18.375 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2858 | 分页数据加载成功（数据库）: 50条数据，第1页，总计1396条
2025-06-26 15:22:18.393 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_05_active_employees 分页获取数据: 第2页, 每页50条
2025-06-26 15:22:18.393 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 15:22:18.399 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2702 | 表 salary_data_2026_05_active_employees 无字段偏好设置，显示所有字段
2025-06-26 15:22:18.400 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-26 15:22:18.403 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 13 -> 50
2025-06-26 15:22:18.406 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_05_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-26 15:22:18.409 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 15:22:18.425 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 15:22:18.428 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 15:22:18.463 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 59.86ms
2025-06-26 15:22:18.468 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-26 15:22:18.468 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 1396
2025-06-26 15:22:25.471 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-26 15:22:25.471 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 15:22:25.471 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 15:22:25.471 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_05_active_employees 分页获取数据: 第2页, 每页50条
2025-06-26 15:22:25.471 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_05_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-26 15:22:25.471 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 50
2025-06-26 15:22:25.471 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 15:22:25.471 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 15:22:25.471 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 15:22:30.477 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 5006.74ms
2025-06-26 15:22:30.477 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第2页, 显示50条记录，字段数: 16
2025-06-26 15:22:30.477 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 2
2025-06-26 15:22:45.951 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2026年 > 5月', '工资表 > 2025年', '工资表 > 2026年', '工资表', '异动人员表']
2025-06-26 15:22:45.951 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 5月
2025-06-26 15:22:45.951 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 15:22:45.951 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 50
2025-06-26 15:22:45.951 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 7 个表头
2025-06-26 15:22:45.951 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 15:22:45.967 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 50, 耗时: 15.68ms
2025-06-26 15:22:45.967 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 15:22:45.967 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 15:22:45.967 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 15:22:45.967 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 50
2025-06-26 15:22:45.967 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 7 个表头
2025-06-26 15:22:45.967 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 15:22:45.967 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 50, 耗时: 0.00ms
2025-06-26 15:22:45.967 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 15:22:45.967 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 15:22:45.967 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 5月
2025-06-26 15:22:49.264 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data:396 | 数据导入功能被触发，发出 import_requested 信号。
2025-06-26 15:22:49.264 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:2430 | 接收到数据导入请求，推断的目标路径: 工资表 > 2026年 > 5月。打开导入对话框。
2025-06-26 15:22:49.264 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-06-26 15:22:49.264 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 15:22:49.264 | INFO     | src.modules.data_import.multi_sheet_importer:__init__:64 | 多Sheet导入器初始化完成
2025-06-26 15:22:49.264 | INFO     | src.gui.widgets.target_selection_widget:_load_available_options:250 | 成功加载导航配置
2025-06-26 15:22:49.300 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:702 | 找到 72 个匹配类型 'salary_data' 的表
2025-06-26 15:22:49.359 | INFO     | src.gui.dialogs:_init_field_mapping:1925 | 初始化字段映射表格: 17 个默认字段
2025-06-26 15:22:49.492 | INFO     | src.modules.data_import.import_defaults_manager:load_settings:86 | 未找到用户设置文件，使用默认设置
2025-06-26 15:22:49.492 | INFO     | src.gui.dialogs:_apply_default_settings:2124 | 已应用默认设置: {'start_row': 1, 'import_mode': 'multi_sheet', 'auto_match_sheet': True, 'include_header': True, 'skip_empty_rows': True, 'create_table_mode': 'sheet_name', 'import_strategy': 'merge_to_single_table', 'table_template': 'salary_data'}
2025-06-26 15:22:49.492 | INFO     | src.gui.dialogs:_setup_tooltips:2379 | 工具提示设置完成
2025-06-26 15:22:49.492 | INFO     | src.gui.dialogs:_setup_shortcuts:2418 | 快捷键设置完成
2025-06-26 15:22:49.492 | INFO     | src.gui.dialogs:__init__:77 | 数据导入对话框初始化完成。
2025-06-26 15:22:54.997 | INFO     | __main__:main:302 | 应用程序正常退出
2025-06-26 15:23:07.123 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-26 15:23:07.123 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-26 15:23:07.123 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-26 15:23:07.123 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-26 15:23:07.123 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-26 15:23:07.123 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-26 15:23:08.362 | INFO     | __main__:setup_app_logging:206 | 月度工资异动处理系统 v2.0.0-refactored 启动
2025-06-26 15:23:08.362 | INFO     | __main__:main:270 | 初始化核心管理器...
2025-06-26 15:23:08.362 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 15:23:08.362 | INFO     | src.modules.system_config.config_manager:load_config:338 | 正在加载配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 15:23:08.362 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:252 | 配置文件验证通过
2025-06-26 15:23:08.362 | INFO     | src.modules.system_config.config_manager:load_config:350 | 配置文件加载成功
2025-06-26 15:23:08.362 | INFO     | src.modules.data_storage.database_manager:_initialize_database:101 | 开始初始化数据库...
2025-06-26 15:23:08.389 | INFO     | src.modules.data_storage.database_manager:_initialize_database:156 | 数据库初始化完成
2025-06-26 15:23:08.396 | INFO     | src.modules.data_storage.database_manager:__init__:97 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-06-26 15:23:08.399 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 15:23:08.400 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:102 | 动态表管理器初始化完成
2025-06-26 15:23:08.401 | INFO     | __main__:main:275 | 核心管理器初始化完成。
2025-06-26 15:23:08.402 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成 - 最大条目数: 50, 最大内存: 50MB
2025-06-26 15:23:08.402 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-06-26 15:23:08.402 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-06-26 15:23:08.674 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:1493 | 菜单栏创建完成
2025-06-26 15:23:08.674 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 15:23:08.674 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设置: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 15:23:08.674 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成功
2025-06-26 15:23:08.689 | INFO     | src.gui.prototype.prototype_main_window:__init__:1469 | 菜单栏管理器初始化完成
2025-06-26 15:23:08.689 | INFO     | src.gui.table_header_manager:__init__:68 | 表头管理器初始化完成
2025-06-26 15:23:08.689 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:2170 | 管理器设置完成，包含增强版表头管理器
2025-06-26 15:23:08.689 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_state:151 | 导航状态加载成功
2025-06-26 15:23:08.689 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-06-26 15:23:08.739 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:_load_data:556 | 数据加载成功: state/user/tree_expansion_data.json
2025-06-26 15:23:08.739 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:148 | 智能树形展开算法初始化完成
2025-06-26 15:23:08.748 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:916 | 开始从元数据动态加载工资数据...
2025-06-26 15:23:08.749 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:923 | 检测到首次启动，将延迟加载导航数据确保数据库就绪
2025-06-26 15:23:08.753 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:654 | 导航面板已重构：移除功能性导航，专注数据导航
2025-06-26 15:23:08.794 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:689 | 恢复导航状态: 91个展开项
2025-06-26 15:23:08.803 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2024年', '异动人员表 > 2024年', '工资表', '异动人员表']
2025-06-26 15:23:08.803 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:495 | 增强导航面板初始化完成
2025-06-26 15:23:08.873 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1264 | 快捷键注册完成: 18/18 个
2025-06-26 15:23:08.874 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1475 | 拖拽排序管理器初始化完成
2025-06-26 15:23:08.876 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 15:23:08.876 | INFO     | src.modules.data_import.header_edit_manager:__init__:74 | 表头编辑管理器初始化完成
2025-06-26 15:23:08.878 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1851 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-06-26 15:23:08.878 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 15:23:08.880 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 15:23:08.886 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 8.84ms
2025-06-26 15:23:08.886 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 15:23:08.895 | INFO     | src.gui.widgets.pagination_widget:__init__:88 | 分页组件初始化完成
2025-06-26 15:23:08.944 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:379 | 控制面板按钮信号连接完成
2025-06-26 15:23:08.960 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 15:23:08.961 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 15:23:08.962 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:2143 | 快捷键设置完成
2025-06-26 15:23:08.962 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:2132 | 主窗口UI设置完成。
2025-06-26 15:23:08.963 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:2198 | 信号连接设置完成
2025-06-26 15:23:08.965 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 15:23:09.030 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:2627 | 已加载字段映射信息，共48个表的映射
2025-06-26 15:23:09.030 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 15:23:09.031 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 15:23:09.032 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 15:23:09.032 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 1.06ms
2025-06-26 15:23:09.034 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 15:23:09.034 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 15:23:09.034 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 15:23:09.034 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 15:23:09.038 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 15:23:09.038 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 4.27ms
2025-06-26 15:23:09.061 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 15:23:09.063 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 15:23:09.064 | INFO     | src.gui.prototype.prototype_main_window:__init__:2084 | 原型主窗口初始化完成
2025-06-26 15:23:09.466 | INFO     | __main__:main:297 | 应用程序启动成功
2025-06-26 15:23:09.472 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1266px)
2025-06-26 15:23:09.472 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:1241 | MainWorkspaceArea 响应式适配: sm
2025-06-26 15:23:09.556 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_load_salary_data:938 | 执行延迟的工资数据加载...
2025-06-26 15:23:09.557 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1036 | 开始强制刷新工资数据导航... (尝试 1/3)
2025-06-26 15:23:09.559 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1044 | 找到工资表节点: 📊 工资表
2025-06-26 15:23:09.604 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:702 | 找到 72 个匹配类型 'salary_data' 的表
2025-06-26 15:23:09.606 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2026年，包含 6 个月份
2025-06-26 15:23:09.607 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2025年，包含 3 个月份
2025-06-26 15:23:09.607 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2024年，包含 1 个月份
2025-06-26 15:23:09.608 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2023年，包含 1 个月份
2025-06-26 15:23:09.608 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2022年，包含 1 个月份
2025-06-26 15:23:09.609 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2021年，包含 1 个月份
2025-06-26 15:23:09.609 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2020年，包含 1 个月份
2025-06-26 15:23:09.610 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2019年，包含 1 个月份
2025-06-26 15:23:09.611 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2018年，包含 1 个月份
2025-06-26 15:23:09.611 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2017年，包含 1 个月份
2025-06-26 15:23:09.612 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2016年，包含 1 个月份
2025-06-26 15:23:09.613 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1149 | 工资数据导航已强制刷新: 11 个年份, 18 个月份
2025-06-26 15:23:09.613 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1152 | 默认路径: 工资表 > 2026年 > 1月 > 全部在职人员
2025-06-26 15:23:09.614 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1191 | force_refresh_salary_data 执行完成
2025-06-26 15:23:43.960 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表', '异动人员表', '异动人员表 > 2025年 > 4月', '异动人员表 > 2025年 > 5月']
2025-06-26 15:23:43.960 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年
2025-06-26 15:23:43.960 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 15:23:43.960 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 15:23:43.975 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 15:23:43.975 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 15.67ms
2025-06-26 15:23:43.975 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 15:23:43.975 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 15:23:43.975 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 15:23:43.975 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 15:23:43.975 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 15:23:43.975 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 0.00ms
2025-06-26 15:23:43.975 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 15:23:43.975 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 15:23:43.975 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年
2025-06-26 15:23:48.464 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data:396 | 数据导入功能被触发，发出 import_requested 信号。
2025-06-26 15:23:48.464 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:2430 | 接收到数据导入请求，推断的目标路径: 工资表 > 2026年。打开导入对话框。
2025-06-26 15:23:48.465 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-06-26 15:23:48.466 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 15:23:48.467 | INFO     | src.modules.data_import.multi_sheet_importer:__init__:64 | 多Sheet导入器初始化完成
2025-06-26 15:23:48.468 | INFO     | src.gui.widgets.target_selection_widget:_load_available_options:250 | 成功加载导航配置
2025-06-26 15:23:48.504 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:702 | 找到 72 个匹配类型 'salary_data' 的表
2025-06-26 15:23:48.577 | INFO     | src.gui.dialogs:_init_field_mapping:1925 | 初始化字段映射表格: 17 个默认字段
2025-06-26 15:23:48.664 | INFO     | src.modules.data_import.import_defaults_manager:load_settings:86 | 未找到用户设置文件，使用默认设置
2025-06-26 15:23:48.664 | INFO     | src.gui.dialogs:_apply_default_settings:2124 | 已应用默认设置: {'start_row': 1, 'import_mode': 'multi_sheet', 'auto_match_sheet': True, 'include_header': True, 'skip_empty_rows': True, 'create_table_mode': 'sheet_name', 'import_strategy': 'merge_to_single_table', 'table_template': 'salary_data'}
2025-06-26 15:23:48.665 | INFO     | src.gui.dialogs:_setup_tooltips:2379 | 工具提示设置完成
2025-06-26 15:23:48.667 | INFO     | src.gui.dialogs:_setup_shortcuts:2418 | 快捷键设置完成
2025-06-26 15:23:48.667 | INFO     | src.gui.dialogs:__init__:77 | 数据导入对话框初始化完成。
2025-06-26 15:23:54.637 | INFO     | src.gui.dialogs:_on_target_changed:2080 | 目标位置已更新: 工资表 > 2026年 > 6月 > 全部在职人员
2025-06-26 15:24:20.502 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-26 15:24:29.874 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-26 15:24:29.890 | INFO     | src.modules.data_import.smart_sheet_matcher:record_user_choice:372 | 记录用户选择: 全部在职人员 -> 全部在职人员工资表
2025-06-26 15:24:29.890 | INFO     | src.gui.dialogs:_auto_select_sheet_by_category:2159 | 根据人员类别 '全部在职人员' 自动选择工作表: 全部在职人员工资表 (匹配类型: fuzzy, 得分: 0.60)
2025-06-26 15:25:42.982 | INFO     | src.modules.data_import.excel_importer:preview_data:221 | 数据预览完成: 23列, 20行
2025-06-26 15:26:04.785 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-26 15:26:05.010 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-26 15:26:05.022 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-26 15:26:05.022 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:194 | 开始导入多Sheet Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-26 15:26:05.022 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-26 15:26:05.230 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-26 15:26:05.230 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:205 | 检测到 4 个工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-26 15:26:05.230 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-26 15:26:05.230 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-26 15:26:05.230 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-06-26 15:26:05.343 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:315 | 导入完成: 3行 x 12列
2025-06-26 15:26:05.346 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-26 15:26:05.347 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:527 | 过滤记录[行3]: 姓名字段(姓名)为空, 数据: {'序号': '总计', '合计': np.float64(34405.100000000006)}
2025-06-26 15:26:05.347 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:565 | 数据过滤完成: 原始3条记录，过滤1条无效记录，有效记录2条
2025-06-26 15:26:05.348 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:415 | 未找到工作表 离休人员工资表 的配置，使用智能默认处理
2025-06-26 15:26:05.348 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:706 | 为Sheet '离休人员工资表' 生成智能默认配置: 1 个必需字段
2025-06-26 15:26:05.349 | INFO     | src.modules.data_import.auto_field_mapping_generator:generate_mapping:84 | 开始为表 'salary_data_2026_06_retired_employees' 生成字段映射，共 12 个字段
2025-06-26 15:26:05.362 | INFO     | src.modules.data_import.auto_field_mapping_generator:generate_mapping:131 | 字段映射生成完成，详细信息:
2025-06-26 15:26:05.371 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:143 | 字段映射保存成功: salary_data_2026_06_retired_employees
2025-06-26 15:26:05.371 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:467 | 为表 salary_data_2026_06_retired_employees 自动生成字段映射: 8 个字段
2025-06-26 15:26:05.372 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:488 | 映射质量评估: 覆盖率=100.0%, 总体评分=1.00
2025-06-26 15:26:05.372 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:494 | Sheet 离休人员工资表 存在 1 个验证错误
2025-06-26 15:26:05.376 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:503 | Sheet 离休人员工资表 数据处理完成: 2 行
2025-06-26 15:26:05.376 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:840 | 列名映射成功: ['人员代码', '姓名'] -> ['employee_id', 'employee_name']
2025-06-26 15:26:05.376 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:853 | 表 salary_data_2026_06_retired_employees 不存在，将根据模板创建...
2025-06-26 15:26:05.397 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:951 | 成功向表 salary_data_2026_06_retired_employees 保存 2 条数据。
2025-06-26 15:26:05.403 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-26 15:26:05.415 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-26 15:26:05.416 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-06-26 15:26:05.530 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:315 | 导入完成: 14行 x 21列
2025-06-26 15:26:05.530 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-26 15:26:05.530 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:527 | 过滤记录[行14]: 姓名字段(姓名)为空, 数据: {'序号': '总计', '应发工资': np.float64(33607.14625)}
2025-06-26 15:26:05.530 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:565 | 数据过滤完成: 原始14条记录，过滤1条无效记录，有效记录13条
2025-06-26 15:26:05.530 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:415 | 未找到工作表 退休人员工资表 的配置，使用智能默认处理
2025-06-26 15:26:05.530 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:706 | 为Sheet '退休人员工资表' 生成智能默认配置: 1 个必需字段
2025-06-26 15:26:05.530 | INFO     | src.modules.data_import.auto_field_mapping_generator:generate_mapping:84 | 开始为表 'salary_data_2026_06_pension_employees' 生成字段映射，共 21 个字段
2025-06-26 15:26:05.546 | INFO     | src.modules.data_import.auto_field_mapping_generator:generate_mapping:131 | 字段映射生成完成，详细信息:
2025-06-26 15:26:05.546 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:143 | 字段映射保存成功: salary_data_2026_06_pension_employees
2025-06-26 15:26:05.546 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:467 | 为表 salary_data_2026_06_pension_employees 自动生成字段映射: 17 个字段
2025-06-26 15:26:05.546 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:488 | 映射质量评估: 覆盖率=100.0%, 总体评分=1.00
2025-06-26 15:26:05.546 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:494 | Sheet 退休人员工资表 存在 1 个验证错误
2025-06-26 15:26:05.564 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:503 | Sheet 退休人员工资表 数据处理完成: 13 行
2025-06-26 15:26:05.566 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:840 | 列名映射成功: ['人员代码', '姓名', '津贴', '应发工资'] -> ['employee_id', 'employee_name', 'allowance', 'total_salary']
2025-06-26 15:26:05.570 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:853 | 表 salary_data_2026_06_pension_employees 不存在，将根据模板创建...
2025-06-26 15:26:05.596 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:951 | 成功向表 salary_data_2026_06_pension_employees 保存 13 条数据。
2025-06-26 15:26:05.598 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-26 15:26:05.598 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-26 15:26:05.598 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-06-26 15:26:05.749 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:315 | 导入完成: 1397行 x 23列
2025-06-26 15:26:05.749 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-26 15:26:05.749 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:527 | 过滤记录[行1397]: 姓名字段(姓名)为空, 数据: {'2025年岗位工资': np.float64(3971045.71), '2025年薪级工资': np.int64(3138129), '津贴': np.float64(94436.73000000001), '结余津贴': np.int64(78008), '2025年基础性绩效': np.int64(4706933), '卫生费': np.float64(14240.0), '交通补贴': np.float64(306460.0), '物业补贴': np.float64(305860.0), '住房补贴': np.float64(337019.8710385144), '车补': np.float64(12870.0), '通讯补贴': np.float64(10250.0), '2025年奖励性绩效预发': np.int64(2262100), '补发': np.float64(2528.0), '借支': np.float64(122740.12055172413), '应发工资': np.float64(15117140.190486807), '2025公积金': np.int64(2390358), '代扣代存养老保险': np.float64(1967911.5299999986)}
2025-06-26 15:26:05.749 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:565 | 数据过滤完成: 原始1397条记录，过滤1条无效记录，有效记录1396条
2025-06-26 15:26:05.749 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:415 | 未找到工作表 全部在职人员工资表 的配置，使用智能默认处理
2025-06-26 15:26:05.749 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:706 | 为Sheet '全部在职人员工资表' 生成智能默认配置: 2 个必需字段
2025-06-26 15:26:05.749 | INFO     | src.modules.data_import.auto_field_mapping_generator:generate_mapping:84 | 开始为表 'salary_data_2026_06_active_employees' 生成字段映射，共 23 个字段
2025-06-26 15:26:05.765 | INFO     | src.modules.data_import.auto_field_mapping_generator:generate_mapping:131 | 字段映射生成完成，详细信息:
2025-06-26 15:26:05.765 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:143 | 字段映射保存成功: salary_data_2026_06_active_employees
2025-06-26 15:26:05.765 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:467 | 为表 salary_data_2026_06_active_employees 自动生成字段映射: 16 个字段
2025-06-26 15:26:05.782 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:488 | 映射质量评估: 覆盖率=100.0%, 总体评分=1.00
2025-06-26 15:26:05.782 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:494 | Sheet 全部在职人员工资表 存在 2 个验证错误
2025-06-26 15:26:05.789 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:503 | Sheet 全部在职人员工资表 数据处理完成: 1396 行
2025-06-26 15:26:05.790 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:840 | 列名映射成功: ['工号', '姓名', '津贴', '应发工资'] -> ['employee_id', 'employee_name', 'allowance', 'total_salary']
2025-06-26 15:26:05.792 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:853 | 表 salary_data_2026_06_active_employees 不存在，将根据模板创建...
2025-06-26 15:26:05.846 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:951 | 成功向表 salary_data_2026_06_active_employees 保存 1396 条数据。
2025-06-26 15:26:05.852 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-26 15:26:05.856 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-26 15:26:05.857 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-06-26 15:26:05.993 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:315 | 导入完成: 63行 x 17列
2025-06-26 15:26:05.995 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-26 15:26:05.996 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:527 | 过滤记录[行63]: 姓名字段(姓名)为空, 数据: {'应发工资': np.float64(471980.9)}
2025-06-26 15:26:05.997 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:565 | 数据过滤完成: 原始63条记录，过滤1条无效记录，有效记录62条
2025-06-26 15:26:05.998 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:415 | 未找到工作表 A岗职工 的配置，使用智能默认处理
2025-06-26 15:26:05.998 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:706 | 为Sheet 'A岗职工' 生成智能默认配置: 2 个必需字段
2025-06-26 15:26:05.998 | INFO     | src.modules.data_import.auto_field_mapping_generator:generate_mapping:84 | 开始为表 'salary_data_2026_06_a_grade_employees' 生成字段映射，共 17 个字段
2025-06-26 15:26:05.998 | INFO     | src.modules.data_import.auto_field_mapping_generator:generate_mapping:131 | 字段映射生成完成，详细信息:
2025-06-26 15:26:05.998 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:143 | 字段映射保存成功: salary_data_2026_06_a_grade_employees
2025-06-26 15:26:05.998 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:467 | 为表 salary_data_2026_06_a_grade_employees 自动生成字段映射: 13 个字段
2025-06-26 15:26:06.014 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:488 | 映射质量评估: 覆盖率=100.0%, 总体评分=1.00
2025-06-26 15:26:06.014 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:494 | Sheet A岗职工 存在 2 个验证错误
2025-06-26 15:26:06.014 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:503 | Sheet A岗职工 数据处理完成: 62 行
2025-06-26 15:26:06.014 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:840 | 列名映射成功: ['工号', '姓名', '津贴', '应发工资'] -> ['employee_id', 'employee_name', 'allowance', 'total_salary']
2025-06-26 15:26:06.014 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:853 | 表 salary_data_2026_06_a_grade_employees 不存在，将根据模板创建...
2025-06-26 15:26:06.034 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:951 | 成功向表 salary_data_2026_06_a_grade_employees 保存 62 条数据。
2025-06-26 15:26:06.035 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:224 | 多Sheet导入完成: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2026_06_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2026_06_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2026_06_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2026_06_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2026_06_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2026_06_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2026_06_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2026_06_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 6}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']}
2025-06-26 15:26:06.037 | INFO     | src.gui.dialogs:_execute_multi_sheet_import:1479 | 多Sheet导入成功: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2026_06_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2026_06_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2026_06_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2026_06_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2026_06_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2026_06_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2026_06_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2026_06_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 6}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工'], 'import_mode': 'multi_sheet', 'strategy': 'separate_tables', 'data_period': '2026-06', 'data_description': '2026年6月工资数据', 'file_path': 'C:/test/salary_changes/salary_changes/data/工资表/2025年5月份正式工工资（报财务)  最终版.xls', 'target_path': '工资表 > 2026年 > 6月 > 全部在职人员'}
2025-06-26 15:26:06.044 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:2443 | 开始处理导入结果: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2026_06_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2026_06_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2026_06_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2026_06_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2026_06_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2026_06_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2026_06_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2026_06_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 6}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工'], 'import_mode': 'multi_sheet', 'strategy': 'separate_tables', 'data_period': '2026-06', 'data_description': '2026年6月工资数据', 'file_path': 'C:/test/salary_changes/salary_changes/data/工资表/2025年5月份正式工工资（报财务)  最终版.xls', 'target_path': '工资表 > 2026年 > 6月 > 全部在职人员'}
2025-06-26 15:26:06.059 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:2454 | 导入模式: multi_sheet, 目标路径: '工资表 > 2026年 > 6月 > 全部在职人员'
2025-06-26 15:26:06.063 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:2462 | 接收到导入数据, 来源: 未知来源, 目标路径: 工资表 > 2026年 > 6月 > 全部在职人员
2025-06-26 15:26:06.067 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:2520 | 检查是否需要更新导航面板: ['工资表', '2026年', '6月', '全部在职人员']
2025-06-26 15:26:06.068 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:2524 | 检测到工资数据导入，开始刷新导航面板
2025-06-26 15:26:06.068 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:2528 | 使用强制刷新方法
2025-06-26 15:26:06.072 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1036 | 开始强制刷新工资数据导航... (尝试 1/3)
2025-06-26 15:26:06.073 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1044 | 找到工资表节点: 📊 工资表
2025-06-26 15:26:06.084 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2026年', '工资表', '异动人员表', '异动人员表 > 2025年 > 4月']
2025-06-26 15:26:06.085 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2025年
2025-06-26 15:26:06.086 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 15:26:06.086 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 15:26:06.089 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 15:26:06.089 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 2.04ms
2025-06-26 15:26:06.093 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 15:26:06.096 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 15:26:06.098 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 15:26:06.113 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 15:26:06.117 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 15:26:06.119 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 5.77ms
2025-06-26 15:26:06.120 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 15:26:06.121 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 15:26:06.124 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2025年
2025-06-26 15:26:06.134 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2024年', '工资表 > 2026年', '异动人员表 > 2025年 > 5月', '工资表']
2025-06-26 15:26:06.135 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2024年
2025-06-26 15:26:06.135 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 15:26:06.136 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 15:26:06.138 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 15:26:06.139 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 1.58ms
2025-06-26 15:26:06.139 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 15:26:06.140 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 15:26:06.143 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 15:26:06.145 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 15:26:06.158 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 15:26:06.161 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 15.13ms
2025-06-26 15:26:06.164 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 15:26:06.165 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 15:26:06.165 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2024年
2025-06-26 15:26:06.176 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2024年', '工资表 > 2026年', '工资表', '异动人员表']
2025-06-26 15:26:06.177 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2023年
2025-06-26 15:26:06.177 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 15:26:06.178 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 15:26:06.180 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 15:26:06.180 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 1.54ms
2025-06-26 15:26:06.181 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 15:26:06.181 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 15:26:06.182 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 15:26:06.182 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 15:26:06.184 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 15:26:06.186 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 4.01ms
2025-06-26 15:26:06.189 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 15:26:06.205 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 15:26:06.209 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2023年
2025-06-26 15:26:06.220 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2024年', '工资表 > 2026年', '工资表', '异动人员表']
2025-06-26 15:26:06.221 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2022年
2025-06-26 15:26:06.222 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 15:26:06.223 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 15:26:06.224 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 15:26:06.225 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 2.07ms
2025-06-26 15:26:06.225 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 15:26:06.226 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 15:26:06.226 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 15:26:06.226 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 15:26:06.228 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 15:26:06.229 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 3.72ms
2025-06-26 15:26:06.229 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 15:26:06.232 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 15:26:06.233 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2022年
2025-06-26 15:26:06.250 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2024年', '工资表 > 2026年', '工资表', '异动人员表']
2025-06-26 15:26:06.259 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2021年
2025-06-26 15:26:06.260 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 15:26:06.260 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 15:26:06.261 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 15:26:06.261 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 0.58ms
2025-06-26 15:26:06.264 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 15:26:06.266 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 15:26:06.266 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 15:26:06.266 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 15:26:06.268 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 15:26:06.268 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 2.07ms
2025-06-26 15:26:06.270 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 15:26:06.270 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 15:26:06.271 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2021年
2025-06-26 15:26:06.282 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2024年', '工资表 > 2026年', '工资表', '异动人员表']
2025-06-26 15:26:06.286 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2020年
2025-06-26 15:26:06.292 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 15:26:06.294 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 15:26:06.299 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 15:26:06.301 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 5.71ms
2025-06-26 15:26:06.301 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 15:26:06.302 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 15:26:06.302 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 15:26:06.302 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 15:26:06.306 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 15:26:06.307 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 5.01ms
2025-06-26 15:26:06.308 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 15:26:06.309 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 15:26:06.309 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2020年
2025-06-26 15:26:06.318 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2024年', '工资表 > 2026年', '工资表', '异动人员表']
2025-06-26 15:26:06.318 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2019年
2025-06-26 15:26:06.319 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 15:26:06.319 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 15:26:06.323 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 15:26:06.327 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 8.31ms
2025-06-26 15:26:06.329 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 15:26:06.332 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 15:26:06.335 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 15:26:06.339 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 15:26:06.343 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 15:26:06.347 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 7.81ms
2025-06-26 15:26:06.348 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 15:26:06.348 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 15:26:06.351 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2019年
2025-06-26 15:26:06.361 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2024年', '工资表 > 2026年', '工资表', '异动人员表']
2025-06-26 15:26:06.362 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2018年
2025-06-26 15:26:06.362 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 15:26:06.362 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 15:26:06.364 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 15:26:06.364 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 2.07ms
2025-06-26 15:26:06.364 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 15:26:06.365 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 15:26:06.365 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 15:26:06.366 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 15:26:06.367 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 15:26:06.370 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 3.66ms
2025-06-26 15:26:06.375 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 15:26:06.379 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 15:26:06.394 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2018年
2025-06-26 15:26:06.406 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2024年', '工资表 > 2026年', '工资表', '异动人员表']
2025-06-26 15:26:06.406 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2017年
2025-06-26 15:26:06.407 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 15:26:06.408 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 15:26:06.409 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 15:26:06.410 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 2.02ms
2025-06-26 15:26:06.410 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 15:26:06.411 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 15:26:06.411 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 15:26:06.411 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 15:26:06.413 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 15:26:06.414 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 3.14ms
2025-06-26 15:26:06.414 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 15:26:06.417 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 15:26:06.418 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2017年
2025-06-26 15:26:06.439 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2024年', '工资表 > 2026年', '工资表', '异动人员表']
2025-06-26 15:26:06.442 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2016年
2025-06-26 15:26:06.443 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 15:26:06.443 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 15:26:06.446 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 15:26:06.447 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 4.24ms
2025-06-26 15:26:06.448 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 15:26:06.448 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 15:26:06.448 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 15:26:06.449 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 15:26:06.450 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 15:26:06.451 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 2.07ms
2025-06-26 15:26:06.452 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 15:26:06.453 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 15:26:06.453 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2016年
2025-06-26 15:26:06.464 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2024年', '工资表', '工资表 > 2026年', '异动人员表']
2025-06-26 15:26:06.466 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表
2025-06-26 15:26:06.470 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 15:26:06.472 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 15:26:06.481 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 15:26:06.489 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 16.20ms
2025-06-26 15:26:06.489 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 15:26:06.489 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 15:26:06.493 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 15:26:06.493 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 15:26:06.497 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 15:26:06.497 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 4.00ms
2025-06-26 15:26:06.498 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 15:26:06.498 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 15:26:06.499 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表
2025-06-26 15:26:06.550 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:702 | 找到 76 个匹配类型 'salary_data' 的表
2025-06-26 15:26:06.551 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2026年，包含 7 个月份
2025-06-26 15:26:06.552 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2025年，包含 3 个月份
2025-06-26 15:26:06.552 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2024年，包含 1 个月份
2025-06-26 15:26:06.554 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2023年，包含 1 个月份
2025-06-26 15:26:06.554 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2022年，包含 1 个月份
2025-06-26 15:26:06.555 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2021年，包含 1 个月份
2025-06-26 15:26:06.558 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2020年，包含 1 个月份
2025-06-26 15:26:06.559 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2019年，包含 1 个月份
2025-06-26 15:26:06.571 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2018年，包含 1 个月份
2025-06-26 15:26:06.575 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2017年，包含 1 个月份
2025-06-26 15:26:06.577 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2016年，包含 1 个月份
2025-06-26 15:26:06.579 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1149 | 工资数据导航已强制刷新: 11 个年份, 19 个月份
2025-06-26 15:26:06.579 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1152 | 默认路径: 工资表 > 2026年 > 1月 > 全部在职人员
2025-06-26 15:26:06.579 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1191 | force_refresh_salary_data 执行完成
2025-06-26 15:26:06.583 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:2533 | 将在800ms后导航到: 工资表 > 2026年 > 6月 > 全部在职人员
2025-06-26 15:26:07.384 | INFO     | src.gui.prototype.prototype_main_window:_navigate_to_imported_path:2566 | 尝试导航到新导入的路径: 工资表 > 2026年 > 6月 > 全部在职人员
2025-06-26 15:26:07.396 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2024年', '工资表', '工资表 > 2026年', '异动人员表']
2025-06-26 15:26:07.399 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 6月 > 全部在职人员
2025-06-26 15:26:07.399 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 15:26:07.400 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2757 | 数据量大(1396条)，启用分页模式
2025-06-26 15:26:07.400 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2785 | 使用分页模式加载 salary_data_2026_06_active_employees，第1页，每页50条
2025-06-26 15:26:07.401 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2828 | 缓存未命中，从数据库加载: salary_data_2026_06_active_employees 第1页
2025-06-26 15:26:07.401 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 6月 > 全部在职人员
2025-06-26 15:26:07.402 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2026_06_active_employees 第1页数据，每页50条
2025-06-26 15:26:07.403 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_06_active_employees 分页获取数据: 第1页, 每页50条
2025-06-26 15:26:07.405 | INFO     | src.gui.prototype.prototype_main_window:_navigate_to_imported_path:2571 | 已成功导航到新导入的路径: 工资表 > 2026年 > 6月 > 全部在职人员
2025-06-26 15:26:07.412 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_06_active_employees 获取第1页数据: 50 行，总计1396行
2025-06-26 15:26:07.412 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 15:26:07.414 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2662 | 应用字段映射到表 salary_data_2026_06_active_employees: 7 个字段重命名
2025-06-26 15:26:07.415 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 1396
2025-06-26 15:26:07.419 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2858 | 分页数据加载成功（数据库）: 50条数据，第1页，总计1396条
2025-06-26 15:26:07.427 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_06_active_employees 分页获取数据: 第2页, 每页50条
2025-06-26 15:26:07.427 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 15:26:07.438 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_06_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-26 15:26:07.441 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2702 | 表 salary_data_2026_06_active_employees 无字段偏好设置，显示所有字段
2025-06-26 15:26:07.454 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-26 15:26:07.458 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 1000 -> 50
2025-06-26 15:26:07.459 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 15:26:07.461 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 15:26:07.462 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 15:26:07.491 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 32.16ms
2025-06-26 15:26:07.512 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-26 15:26:07.514 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 1396
2025-06-26 15:26:38.358 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-26 15:26:38.358 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 15:26:38.358 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 15:26:38.358 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_06_active_employees 分页获取数据: 第2页, 每页50条
2025-06-26 15:26:38.358 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_06_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-26 15:26:38.358 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 50
2025-06-26 15:26:38.358 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 15:26:38.358 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 15:26:38.358 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 15:26:43.095 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 4737.08ms
2025-06-26 15:26:43.095 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第2页, 显示50条记录，字段数: 16
2025-06-26 15:26:43.095 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 2
2025-06-26 15:26:54.274 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-26 15:26:54.274 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 15:26:54.274 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 15:26:54.274 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_06_active_employees 分页获取数据: 第28页, 每页50条
2025-06-26 15:26:54.274 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_06_active_employees 获取第28页数据: 46 行，总计1396行
2025-06-26 15:26:57.719 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 46
2025-06-26 15:26:57.719 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(46)自动调整最大可见行数为: 46
2025-06-26 15:26:57.719 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 15:26:57.719 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 46 行, 16 列
2025-06-26 15:26:57.719 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 46 行, 最大可见行数: 46, 耗时: 3429.64ms
2025-06-26 15:26:57.719 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第28页, 显示46条记录，字段数: 16
2025-06-26 15:26:57.735 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 28
2025-06-26 15:27:13.437 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2024年', '工资表 > 2026年', '工资表', '工资表 > 2026年 > 6月 > 全部在职人员']
2025-06-26 15:27:13.437 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 6月 > 退休人员
2025-06-26 15:27:13.437 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 15:27:13.437 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2760 | 数据量小(13条)，使用普通模式
2025-06-26 15:27:13.437 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 6月 > 退休人员
2025-06-26 15:27:13.437 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2919 | 统一加载所有字段（修复字段不一致问题）
2025-06-26 15:27:13.437 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2026_06_pension_employees 获取数据...
2025-06-26 15:27:13.437 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2026_06_pension_employees 获取 13 行数据。
2025-06-26 15:27:13.437 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2926 | 成功从数据库加载 13 行数据，16 个字段
2025-06-26 15:27:13.459 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2662 | 应用字段映射到表 salary_data_2026_06_pension_employees: 7 个字段重命名
2025-06-26 15:27:13.459 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2931 | 应用字段映射后的表头: ['序号', '人员代码', '姓名', 'id_card', '部门', '人员类别代码', 'basic_salary', 'performance_bonus', 'overtime_pay', '住房补贴', 'deduction', '应发合计', 'month', 'year', 'created_at', 'updated_at']
2025-06-26 15:27:13.459 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 15:27:13.459 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2702 | 表 salary_data_2026_06_pension_employees 无字段偏好设置，显示所有字段
2025-06-26 15:27:13.468 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 13 行
2025-06-26 15:27:14.417 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 46 -> 13
2025-06-26 15:27:14.417 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(13)自动调整最大可见行数为: 13
2025-06-26 15:27:14.419 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 15:27:14.420 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 13 行, 16 列
2025-06-26 15:27:14.432 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 13 行, 最大可见行数: 13, 耗时: 964.23ms
2025-06-26 15:27:14.433 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 13
2025-06-26 15:27:17.919 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2024年', '工资表 > 2026年 > 6月 > 全部在职人员', '工资表 > 2026年', '工资表']
2025-06-26 15:27:17.919 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 6月 > 离休人员
2025-06-26 15:27:17.919 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 15:27:17.919 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2760 | 数据量小(2条)，使用普通模式
2025-06-26 15:27:17.919 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 6月 > 离休人员
2025-06-26 15:27:17.919 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2919 | 统一加载所有字段（修复字段不一致问题）
2025-06-26 15:27:17.919 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2026_06_retired_employees 获取数据...
2025-06-26 15:27:17.919 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2026_06_retired_employees 获取 2 行数据。
2025-06-26 15:27:17.919 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2926 | 成功从数据库加载 2 行数据，16 个字段
2025-06-26 15:27:17.919 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2662 | 应用字段映射到表 salary_data_2026_06_retired_employees: 5 个字段重命名
2025-06-26 15:27:17.919 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2931 | 应用字段映射后的表头: ['序号', '人员代码', '姓名', 'id_card', '部门', 'position', 'basic_salary', 'performance_bonus', 'overtime_pay', '离休\n补贴', 'deduction', 'total_salary', 'month', 'year', 'created_at', 'updated_at']
2025-06-26 15:27:17.919 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 15:27:17.919 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2702 | 表 salary_data_2026_06_retired_employees 无字段偏好设置，显示所有字段
2025-06-26 15:27:17.919 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 2 行
2025-06-26 15:27:17.919 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2583 | 最大可见行数过小可能影响性能，建议至少设置为10，当前值: 2
2025-06-26 15:27:18.009 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 13 -> 2
2025-06-26 15:27:18.009 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(2)自动调整最大可见行数为: 2
2025-06-26 15:27:18.009 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 15:27:18.009 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 2 行, 16 列
2025-06-26 15:27:18.025 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 2 行, 最大可见行数: 2, 耗时: 106.41ms
2025-06-26 15:27:18.025 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 2
2025-06-26 15:27:36.316 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 15:27:36.316 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设置: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 15:27:36.316 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成功
2025-06-26 15:27:36.316 | INFO     | src.modules.system_config.user_preferences:save_preferences:251 | 正在保存偏好设置: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 15:27:36.331 | INFO     | src.modules.system_config.user_preferences:save_preferences:255 | 偏好设置保存成功
2025-06-26 15:27:36.341 | INFO     | src.gui.prototype.prototype_main_window:_on_menu_visibility_changed:2226 | 菜单栏可见性变化: False
2025-06-26 15:27:36.345 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3254 | 已注册 4 个表格到表头管理器
2025-06-26 15:27:36.355 | INFO     | src.gui.prototype.prototype_main_window:refresh_layout:2286 | 窗口布局全面刷新完成
2025-06-26 15:27:36.356 | INFO     | src.gui.prototype.prototype_main_window:hide_menu_bar:1819 | 菜单栏已隐藏
2025-06-26 15:27:36.356 | INFO     | src.gui.prototype.prototype_main_window:_on_settings_clicked:2210 | 设置按钮点击，切换菜单栏显示状态
2025-06-26 15:27:36.855 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2167191467600 已经注册，将覆盖现有注册
2025-06-26 15:27:36.855 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2167191460400 已经注册，将覆盖现有注册
2025-06-26 15:27:36.855 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_2_2167192100240 已经注册，将覆盖现有注册
2025-06-26 15:27:36.855 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_3_2167192132000 已经注册，将覆盖现有注册
2025-06-26 15:27:36.855 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3254 | 已注册 4 个表格到表头管理器
2025-06-26 15:27:38.186 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 15:27:38.186 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设置: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 15:27:38.186 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成功
2025-06-26 15:27:38.186 | INFO     | src.modules.system_config.user_preferences:save_preferences:251 | 正在保存偏好设置: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 15:27:38.186 | INFO     | src.modules.system_config.user_preferences:save_preferences:255 | 偏好设置保存成功
2025-06-26 15:27:38.217 | INFO     | src.gui.prototype.prototype_main_window:_on_menu_visibility_changed:2226 | 菜单栏可见性变化: True
2025-06-26 15:27:38.222 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2167191467600 已经注册，将覆盖现有注册
2025-06-26 15:27:38.222 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2167191460400 已经注册，将覆盖现有注册
2025-06-26 15:27:38.222 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_2_2167192100240 已经注册，将覆盖现有注册
2025-06-26 15:27:38.223 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_3_2167192132000 已经注册，将覆盖现有注册
2025-06-26 15:27:38.224 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3254 | 已注册 4 个表格到表头管理器
2025-06-26 15:27:38.237 | INFO     | src.gui.prototype.prototype_main_window:refresh_layout:2286 | 窗口布局全面刷新完成
2025-06-26 15:27:38.242 | INFO     | src.gui.prototype.prototype_main_window:show_menu_bar:1792 | 菜单栏已显示
2025-06-26 15:27:38.244 | INFO     | src.gui.prototype.prototype_main_window:_on_settings_clicked:2210 | 设置按钮点击，切换菜单栏显示状态
2025-06-26 15:27:38.743 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2167191467600 已经注册，将覆盖现有注册
2025-06-26 15:27:38.743 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2167191460400 已经注册，将覆盖现有注册
2025-06-26 15:27:38.743 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_2_2167192100240 已经注册，将覆盖现有注册
2025-06-26 15:27:38.743 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_3_2167192132000 已经注册，将覆盖现有注册
2025-06-26 15:27:38.743 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3254 | 已注册 4 个表格到表头管理器
2025-06-26 15:27:41.153 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 15:27:41.168 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设置: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 15:27:41.168 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成功
2025-06-26 15:27:41.168 | INFO     | src.modules.system_config.user_preferences:save_preferences:251 | 正在保存偏好设置: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 15:27:41.168 | INFO     | src.modules.system_config.user_preferences:save_preferences:255 | 偏好设置保存成功
2025-06-26 15:27:41.180 | INFO     | src.gui.prototype.prototype_main_window:_on_menu_visibility_changed:2226 | 菜单栏可见性变化: False
2025-06-26 15:27:41.185 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2167191467600 已经注册，将覆盖现有注册
2025-06-26 15:27:41.185 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2167191460400 已经注册，将覆盖现有注册
2025-06-26 15:27:41.186 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_2_2167192100240 已经注册，将覆盖现有注册
2025-06-26 15:27:41.186 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_3_2167192132000 已经注册，将覆盖现有注册
2025-06-26 15:27:41.187 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3254 | 已注册 4 个表格到表头管理器
2025-06-26 15:27:41.211 | INFO     | src.gui.prototype.prototype_main_window:refresh_layout:2286 | 窗口布局全面刷新完成
2025-06-26 15:27:41.318 | INFO     | src.gui.prototype.prototype_main_window:hide_menu_bar:1819 | 菜单栏已隐藏
2025-06-26 15:27:41.318 | INFO     | src.gui.prototype.prototype_main_window:_on_settings_clicked:2210 | 设置按钮点击，切换菜单栏显示状态
2025-06-26 15:27:41.818 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2167191467600 已经注册，将覆盖现有注册
2025-06-26 15:27:41.818 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2167191460400 已经注册，将覆盖现有注册
2025-06-26 15:27:41.818 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_2_2167192100240 已经注册，将覆盖现有注册
2025-06-26 15:27:41.818 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_3_2167192132000 已经注册，将覆盖现有注册
2025-06-26 15:27:41.818 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3254 | 已注册 4 个表格到表头管理器
2025-06-26 15:27:43.899 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 15:27:43.899 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设置: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 15:27:43.899 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成功
2025-06-26 15:27:43.899 | INFO     | src.modules.system_config.user_preferences:save_preferences:251 | 正在保存偏好设置: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 15:27:43.899 | INFO     | src.modules.system_config.user_preferences:save_preferences:255 | 偏好设置保存成功
2025-06-26 15:27:43.929 | INFO     | src.gui.prototype.prototype_main_window:_on_menu_visibility_changed:2226 | 菜单栏可见性变化: True
2025-06-26 15:27:43.936 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2167191467600 已经注册，将覆盖现有注册
2025-06-26 15:27:43.936 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2167191460400 已经注册，将覆盖现有注册
2025-06-26 15:27:43.937 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_2_2167192100240 已经注册，将覆盖现有注册
2025-06-26 15:27:43.937 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_3_2167192132000 已经注册，将覆盖现有注册
2025-06-26 15:27:43.938 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3254 | 已注册 4 个表格到表头管理器
2025-06-26 15:27:43.942 | INFO     | src.gui.prototype.prototype_main_window:refresh_layout:2286 | 窗口布局全面刷新完成
2025-06-26 15:27:43.942 | INFO     | src.gui.prototype.prototype_main_window:show_menu_bar:1792 | 菜单栏已显示
2025-06-26 15:27:43.955 | INFO     | src.gui.prototype.prototype_main_window:_on_settings_clicked:2210 | 设置按钮点击，切换菜单栏显示状态
2025-06-26 15:27:44.443 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2167191467600 已经注册，将覆盖现有注册
2025-06-26 15:27:44.443 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2167191460400 已经注册，将覆盖现有注册
2025-06-26 15:27:44.443 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_2_2167192100240 已经注册，将覆盖现有注册
2025-06-26 15:27:44.443 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_3_2167192132000 已经注册，将覆盖现有注册
2025-06-26 15:27:44.443 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3254 | 已注册 4 个表格到表头管理器
2025-06-26 15:27:47.623 | INFO     | __main__:main:302 | 应用程序正常退出
